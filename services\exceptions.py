"""
Service Layer Exceptions

This module contains all exception classes used by the service layer.
Separating exceptions into their own module prevents circular import issues
between the services package and individual service modules.

Exception Hierarchy:
- ServiceError (base exception)
  - AuthenticationError
  - FileProcessingError
  - QueryProcessingError
  - AnalyticsError
"""


class ServiceError(Exception):
    """Base exception for service layer errors."""
    pass


class AuthenticationError(ServiceError):
    """Authentication-related service errors."""
    pass


class FileProcessingError(ServiceError):
    """File processing-related service errors."""
    pass


class QueryProcessingError(ServiceError):
    """Query processing-related service errors."""
    pass


class AnalyticsError(ServiceError):
    """Analytics-related service errors."""
    pass
