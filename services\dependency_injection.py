"""
Dependency Injection Container

This module provides a comprehensive dependency injection system for the application,
enabling loose coupling, testability, and maintainable architecture.

Features:
- Service registration and resolution
- Singleton and transient lifetimes
- Interface-based dependency injection
- Circular dependency detection
- Service factory support
- Configuration-based service registration
- Automatic service discovery

Version: 1.0.0
"""

import logging
import threading
import inspect
from typing import Any, Dict, Type, TypeVar, Callable, Optional, List, Union
from dataclasses import dataclass
from enum import Enum
import weakref

logger = logging.getLogger(__name__)

T = TypeVar('T')

class ServiceLifetime(Enum):
    """Service lifetime enumeration"""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"

@dataclass
class ServiceDescriptor:
    """Describes a service registration"""
    service_type: Type
    implementation_type: Optional[Type] = None
    factory: Optional[Callable] = None
    instance: Optional[Any] = None
    lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT
    dependencies: List[Type] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []

class CircularDependencyError(Exception):
    """Raised when circular dependencies are detected"""
    pass

class ServiceNotRegisteredError(Exception):
    """Raised when a requested service is not registered"""
    pass

class DependencyInjectionContainer:
    """Dependency injection container for service management"""
    
    def __init__(self):
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._singletons: Dict[Type, Any] = {}
        self._scoped_instances: Dict[Type, Any] = {}
        self._resolution_stack: List[Type] = []
        self._lock = threading.RLock()
        
        # Register the container itself
        self.register_instance(DependencyInjectionContainer, self)
    
    def register_singleton(self, service_type: Type[T], implementation_type: Type[T] = None) -> 'DependencyInjectionContainer':
        """
        Register a service as singleton.
        
        Args:
            service_type: The service interface or type
            implementation_type: The implementation type (defaults to service_type)
            
        Returns:
            Self for method chaining
        """
        return self.register(service_type, implementation_type, ServiceLifetime.SINGLETON)
    
    def register_transient(self, service_type: Type[T], implementation_type: Type[T] = None) -> 'DependencyInjectionContainer':
        """
        Register a service as transient.
        
        Args:
            service_type: The service interface or type
            implementation_type: The implementation type (defaults to service_type)
            
        Returns:
            Self for method chaining
        """
        return self.register(service_type, implementation_type, ServiceLifetime.TRANSIENT)
    
    def register_scoped(self, service_type: Type[T], implementation_type: Type[T] = None) -> 'DependencyInjectionContainer':
        """
        Register a service as scoped.
        
        Args:
            service_type: The service interface or type
            implementation_type: The implementation type (defaults to service_type)
            
        Returns:
            Self for method chaining
        """
        return self.register(service_type, implementation_type, ServiceLifetime.SCOPED)
    
    def register(self, service_type: Type[T], implementation_type: Type[T] = None, 
                lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT) -> 'DependencyInjectionContainer':
        """
        Register a service with specified lifetime.
        
        Args:
            service_type: The service interface or type
            implementation_type: The implementation type (defaults to service_type)
            lifetime: Service lifetime
            
        Returns:
            Self for method chaining
        """
        with self._lock:
            impl_type = implementation_type or service_type
            dependencies = self._get_constructor_dependencies(impl_type)
            
            descriptor = ServiceDescriptor(
                service_type=service_type,
                implementation_type=impl_type,
                lifetime=lifetime,
                dependencies=dependencies
            )
            
            self._services[service_type] = descriptor
            logger.debug(f"Registered {service_type.__name__} as {lifetime.value}")
            
            return self
    
    def register_factory(self, service_type: Type[T], factory: Callable[[], T], 
                        lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT) -> 'DependencyInjectionContainer':
        """
        Register a service with a factory function.
        
        Args:
            service_type: The service type
            factory: Factory function to create the service
            lifetime: Service lifetime
            
        Returns:
            Self for method chaining
        """
        with self._lock:
            descriptor = ServiceDescriptor(
                service_type=service_type,
                factory=factory,
                lifetime=lifetime
            )
            
            self._services[service_type] = descriptor
            logger.debug(f"Registered {service_type.__name__} with factory as {lifetime.value}")
            
            return self
    
    def register_instance(self, service_type: Type[T], instance: T) -> 'DependencyInjectionContainer':
        """
        Register a service instance (singleton).
        
        Args:
            service_type: The service type
            instance: The service instance
            
        Returns:
            Self for method chaining
        """
        with self._lock:
            descriptor = ServiceDescriptor(
                service_type=service_type,
                instance=instance,
                lifetime=ServiceLifetime.SINGLETON
            )
            
            self._services[service_type] = descriptor
            self._singletons[service_type] = instance
            logger.debug(f"Registered {service_type.__name__} instance as singleton")
            
            return self
    
    def resolve(self, service_type: Type[T]) -> T:
        """
        Resolve a service instance.
        
        Args:
            service_type: The service type to resolve
            
        Returns:
            Service instance
            
        Raises:
            ServiceNotRegisteredError: If service is not registered
            CircularDependencyError: If circular dependency is detected
        """
        with self._lock:
            return self._resolve_internal(service_type)
    
    def _resolve_internal(self, service_type: Type[T]) -> T:
        """Internal service resolution with circular dependency detection"""
        # Check for circular dependencies
        if service_type in self._resolution_stack:
            cycle = " -> ".join([t.__name__ for t in self._resolution_stack] + [service_type.__name__])
            raise CircularDependencyError(f"Circular dependency detected: {cycle}")
        
        # Check if service is registered
        if service_type not in self._services:
            raise ServiceNotRegisteredError(f"Service {service_type.__name__} is not registered")
        
        descriptor = self._services[service_type]
        
        # Handle singleton lifetime
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            if service_type in self._singletons:
                return self._singletons[service_type]
            
            # Create singleton instance
            self._resolution_stack.append(service_type)
            try:
                instance = self._create_instance(descriptor)
                self._singletons[service_type] = instance
                return instance
            finally:
                self._resolution_stack.pop()
        
        # Handle scoped lifetime
        elif descriptor.lifetime == ServiceLifetime.SCOPED:
            if service_type in self._scoped_instances:
                return self._scoped_instances[service_type]
            
            # Create scoped instance
            self._resolution_stack.append(service_type)
            try:
                instance = self._create_instance(descriptor)
                self._scoped_instances[service_type] = instance
                return instance
            finally:
                self._resolution_stack.pop()
        
        # Handle transient lifetime
        else:
            self._resolution_stack.append(service_type)
            try:
                return self._create_instance(descriptor)
            finally:
                self._resolution_stack.pop()
    
    def _create_instance(self, descriptor: ServiceDescriptor) -> Any:
        """Create a service instance from descriptor"""
        # Use existing instance if available
        if descriptor.instance is not None:
            return descriptor.instance
        
        # Use factory if available
        if descriptor.factory is not None:
            return descriptor.factory()
        
        # Create instance using constructor injection
        if descriptor.implementation_type:
            # Resolve dependencies
            dependencies = []
            for dep_type in descriptor.dependencies:
                dep_instance = self._resolve_internal(dep_type)
                dependencies.append(dep_instance)
            
            # Create instance
            return descriptor.implementation_type(*dependencies)
        
        raise ValueError(f"Cannot create instance for {descriptor.service_type.__name__}")
    
    def _get_constructor_dependencies(self, implementation_type: Type) -> List[Type]:
        """Get constructor dependencies using type hints"""
        try:
            signature = inspect.signature(implementation_type.__init__)
            dependencies = []
            
            for param_name, param in signature.parameters.items():
                if param_name == 'self':
                    continue
                
                if param.annotation != inspect.Parameter.empty:
                    dependencies.append(param.annotation)
            
            return dependencies
            
        except Exception as e:
            logger.warning(f"Could not analyze dependencies for {implementation_type.__name__}: {e}")
            return []
    
    def clear_scoped(self):
        """Clear scoped service instances"""
        with self._lock:
            self._scoped_instances.clear()
    
    def is_registered(self, service_type: Type) -> bool:
        """Check if a service type is registered"""
        return service_type in self._services
    
    def get_registered_services(self) -> List[Type]:
        """Get list of all registered service types"""
        return list(self._services.keys())
    
    def get_service_info(self, service_type: Type) -> Optional[Dict[str, Any]]:
        """Get information about a registered service"""
        if service_type not in self._services:
            return None
        
        descriptor = self._services[service_type]
        return {
            'service_type': descriptor.service_type.__name__,
            'implementation_type': descriptor.implementation_type.__name__ if descriptor.implementation_type else None,
            'lifetime': descriptor.lifetime.value,
            'has_factory': descriptor.factory is not None,
            'has_instance': descriptor.instance is not None,
            'dependencies': [dep.__name__ for dep in descriptor.dependencies]
        }

# Global container instance
_container: Optional[DependencyInjectionContainer] = None

def get_container() -> DependencyInjectionContainer:
    """Get the global dependency injection container"""
    global _container
    if _container is None:
        _container = DependencyInjectionContainer()
    return _container

def configure_services(container: DependencyInjectionContainer):
    """Configure application services in the DI container"""
    try:
        # Import services (only import what exists)
        try:
            from services.cache_service import get_cache_service
            container.register_factory(type(get_cache_service()), get_cache_service, ServiceLifetime.SINGLETON)
        except ImportError:
            logger.warning("Cache service not available for DI registration")

        try:
            from services.file_io_optimizer import get_file_io_optimizer
            container.register_factory(type(get_file_io_optimizer()), get_file_io_optimizer, ServiceLifetime.SINGLETON)
        except ImportError:
            logger.warning("File I/O optimizer not available for DI registration")

        try:
            from services.image_optimizer import get_image_optimizer
            container.register_factory(type(get_image_optimizer()), get_image_optimizer, ServiceLifetime.SINGLETON)
        except ImportError:
            logger.warning("Image optimizer not available for DI registration")

        try:
            from services.database_optimizer import get_database_pool
            container.register_factory(type(get_database_pool()), get_database_pool, ServiceLifetime.SINGLETON)
        except ImportError:
            logger.warning("Database optimizer not available for DI registration")

        # Register application services if they exist
        try:
            from services.query_service import QueryService
            container.register_singleton(QueryService)
        except ImportError:
            logger.warning("Query service not available for DI registration")

        try:
            from services.analytics_service import AnalyticsService
            container.register_singleton(AnalyticsService)
        except ImportError:
            logger.warning("Analytics service not available for DI registration")

        try:
            from services.file_service import FileService
            container.register_singleton(FileService)
        except ImportError:
            logger.warning("File service not available for DI registration")

        try:
            from services.auth_service import AuthService
            container.register_singleton(AuthService)
        except ImportError:
            logger.warning("Auth service not available for DI registration")

        logger.info("Available services configured in DI container")

    except Exception as e:
        logger.error(f"Error configuring services: {e}")

def resolve(service_type: Type[T]) -> T:
    """Convenience function to resolve a service from the global container"""
    return get_container().resolve(service_type)

def register_singleton(service_type: Type[T], implementation_type: Type[T] = None) -> DependencyInjectionContainer:
    """Convenience function to register a singleton service"""
    return get_container().register_singleton(service_type, implementation_type)

def register_transient(service_type: Type[T], implementation_type: Type[T] = None) -> DependencyInjectionContainer:
    """Convenience function to register a transient service"""
    return get_container().register_transient(service_type, implementation_type)
