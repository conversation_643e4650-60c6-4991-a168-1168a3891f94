"""
File I/O Optimizer Service

This service provides optimized file I/O operations with streaming support for large files,
memory-efficient processing, and performance monitoring.

Features:
- Streaming file processing for large PDFs
- Memory-efficient image processing
- Chunked file operations
- Progress tracking and monitoring
- Automatic memory management
- File size optimization

Version: 1.0.0
"""

import os
import io
import gc
import time
import logging
import hashlib
import tempfile
from typing import Iterator, Optional, Dict, Any, Tuple, BinaryIO
from dataclasses import dataclass
from contextlib import contextmanager
import fitz  # PyMuPDF
from PIL import Image
import psutil

logger = logging.getLogger(__name__)

@dataclass
class ProcessingStats:
    """Statistics for file processing operations"""
    files_processed: int = 0
    total_size_mb: float = 0.0
    processing_time: float = 0.0
    memory_peak_mb: float = 0.0
    memory_saved_mb: float = 0.0
    chunks_processed: int = 0
    
    @property
    def avg_processing_speed_mb_s(self) -> float:
        """Calculate average processing speed in MB/s"""
        return self.total_size_mb / self.processing_time if self.processing_time > 0 else 0.0

class FileIOOptimizer:
    """Optimized file I/O operations with streaming support"""
    
    def __init__(self, chunk_size: int = 8192, max_memory_mb: int = 512):
        """
        Initialize the file I/O optimizer.
        
        Args:
            chunk_size: Size of chunks for streaming operations (bytes)
            max_memory_mb: Maximum memory usage threshold (MB)
        """
        self.chunk_size = chunk_size
        self.max_memory_mb = max_memory_mb
        self.stats = ProcessingStats()
        self.temp_files = []  # Track temporary files for cleanup
        
    def get_file_size_mb(self, file_path: str) -> float:
        """Get file size in MB"""
        try:
            size_bytes = os.path.getsize(file_path)
            return size_bytes / (1024 * 1024)
        except OSError:
            return 0.0
    
    def get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / (1024 * 1024)
        except:
            return 0.0
    
    @contextmanager
    def memory_monitor(self, operation_name: str):
        """Context manager to monitor memory usage during operations"""
        start_memory = self.get_memory_usage_mb()
        start_time = time.time()
        
        try:
            yield
        finally:
            end_memory = self.get_memory_usage_mb()
            end_time = time.time()
            
            # Update statistics
            self.stats.processing_time += (end_time - start_time)
            peak_memory = max(start_memory, end_memory)
            if peak_memory > self.stats.memory_peak_mb:
                self.stats.memory_peak_mb = peak_memory
            
            # Force garbage collection if memory usage is high
            if end_memory > self.max_memory_mb:
                logger.warning(f"High memory usage detected: {end_memory:.2f}MB. Running garbage collection.")
                gc.collect()
            
            logger.debug(f"{operation_name} - Memory: {start_memory:.2f}MB -> {end_memory:.2f}MB, Time: {end_time - start_time:.2f}s")
    
    def stream_file_chunks(self, file_path: str, chunk_size: Optional[int] = None) -> Iterator[bytes]:
        """
        Stream file in chunks to reduce memory usage.
        
        Args:
            file_path: Path to the file to stream
            chunk_size: Size of each chunk (uses default if None)
            
        Yields:
            bytes: File chunks
        """
        chunk_size = chunk_size or self.chunk_size
        
        try:
            with open(file_path, 'rb') as file:
                while True:
                    chunk = file.read(chunk_size)
                    if not chunk:
                        break
                    yield chunk
        except IOError as e:
            logger.error(f"Error streaming file {file_path}: {e}")
            raise
    
    def process_pdf_streaming(self, pdf_path: str, page_callback=None) -> Dict[str, Any]:
        """
        Process PDF with streaming to minimize memory usage.
        
        Args:
            pdf_path: Path to the PDF file
            page_callback: Optional callback function for processing each page
            
        Returns:
            Dictionary with processing results
        """
        file_size_mb = self.get_file_size_mb(pdf_path)
        
        with self.memory_monitor(f"PDF Processing: {os.path.basename(pdf_path)}"):
            try:
                # Open PDF with memory optimization
                doc = fitz.open(pdf_path)
                
                results = {
                    'pages_processed': 0,
                    'total_pages': doc.page_count,
                    'file_size_mb': file_size_mb,
                    'text_extracted': [],
                    'images_extracted': [],
                    'processing_errors': []
                }
                
                # Process pages one at a time to minimize memory usage
                for page_num in range(doc.page_count):
                    try:
                        page = doc[page_num]
                        
                        # Process page with callback if provided
                        if page_callback:
                            page_result = page_callback(page, page_num)
                            if page_result:
                                if 'text' in page_result:
                                    results['text_extracted'].append(page_result['text'])
                                if 'images' in page_result:
                                    results['images_extracted'].extend(page_result['images'])
                        
                        results['pages_processed'] += 1
                        
                        # Force cleanup after each page for large files
                        if file_size_mb > 50:  # For files larger than 50MB
                            page = None  # Release page reference
                            if page_num % 10 == 0:  # Every 10 pages
                                gc.collect()
                        
                    except Exception as e:
                        error_msg = f"Error processing page {page_num + 1}: {str(e)}"
                        logger.error(error_msg)
                        results['processing_errors'].append(error_msg)
                
                doc.close()
                
                # Update statistics
                self.stats.files_processed += 1
                self.stats.total_size_mb += file_size_mb
                
                return results
                
            except Exception as e:
                logger.error(f"Error processing PDF {pdf_path}: {str(e)}")
                raise
    
    def optimize_image_streaming(self, image_path: str, max_size: Tuple[int, int] = (1024, 1024), 
                                quality: int = 85) -> Optional[str]:
        """
        Optimize image with streaming to reduce memory usage.
        
        Args:
            image_path: Path to the image file
            max_size: Maximum dimensions (width, height)
            quality: JPEG quality (1-100)
            
        Returns:
            Path to optimized image or None if failed
        """
        with self.memory_monitor(f"Image Optimization: {os.path.basename(image_path)}"):
            try:
                # Create temporary file for optimized image
                temp_fd, temp_path = tempfile.mkstemp(suffix='.jpg', prefix='optimized_')
                self.temp_files.append(temp_path)
                
                with os.fdopen(temp_fd, 'wb') as temp_file:
                    # Open image with streaming
                    with Image.open(image_path) as img:
                        # Convert to RGB if necessary
                        if img.mode in ('RGBA', 'LA', 'P'):
                            img = img.convert('RGB')
                        
                        # Resize if needed
                        if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                            img.thumbnail(max_size, Image.Resampling.LANCZOS)
                        
                        # Save optimized image
                        img.save(temp_file, format='JPEG', quality=quality, optimize=True)
                
                # Calculate memory savings
                original_size = self.get_file_size_mb(image_path)
                optimized_size = self.get_file_size_mb(temp_path)
                self.stats.memory_saved_mb += max(0, original_size - optimized_size)
                
                logger.debug(f"Image optimized: {original_size:.2f}MB -> {optimized_size:.2f}MB")
                return temp_path
                
            except Exception as e:
                logger.error(f"Error optimizing image {image_path}: {str(e)}")
                return None
    
    def cleanup_temp_files(self):
        """Clean up temporary files created during processing"""
        for temp_path in self.temp_files:
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
            except OSError as e:
                logger.warning(f"Failed to cleanup temp file {temp_path}: {e}")
        
        self.temp_files.clear()
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get comprehensive processing statistics"""
        return {
            'files_processed': self.stats.files_processed,
            'total_size_mb': round(self.stats.total_size_mb, 2),
            'processing_time_seconds': round(self.stats.processing_time, 2),
            'avg_speed_mb_per_second': round(self.stats.avg_processing_speed_mb_s, 2),
            'memory_peak_mb': round(self.stats.memory_peak_mb, 2),
            'memory_saved_mb': round(self.stats.memory_saved_mb, 2),
            'chunks_processed': self.stats.chunks_processed,
            'current_memory_mb': round(self.get_memory_usage_mb(), 2)
        }
    
    def __del__(self):
        """Cleanup when optimizer is destroyed"""
        self.cleanup_temp_files()

# Global optimizer instance
_file_io_optimizer: Optional[FileIOOptimizer] = None

def get_file_io_optimizer() -> FileIOOptimizer:
    """Get global file I/O optimizer instance"""
    global _file_io_optimizer
    if _file_io_optimizer is None:
        # Get configuration from environment
        chunk_size = int(os.getenv('FILE_IO_CHUNK_SIZE', '8192'))
        max_memory_mb = int(os.getenv('FILE_IO_MAX_MEMORY_MB', '512'))
        
        _file_io_optimizer = FileIOOptimizer(
            chunk_size=chunk_size,
            max_memory_mb=max_memory_mb
        )
    
    return _file_io_optimizer
