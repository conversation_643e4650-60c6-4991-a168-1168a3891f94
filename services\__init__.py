"""
Services Package

This package contains business logic services that implement the service layer pattern,
separating business logic from route handlers in the Flask blueprints.

Services:
- auth_service: Authentication and session management business logic
- file_service: File processing and management business logic
- query_service: Query processing and AI interaction business logic
- analytics_service: Analytics and reporting business logic

Architecture Benefits:
- Separation of concerns between presentation and business logic
- Improved testability through isolated business logic
- Better code reusability across different interfaces
- Cleaner route handlers focused on HTTP concerns
- Enhanced maintainability and debugging
"""

# Import exceptions first to avoid circular imports
from .exceptions import (
    ServiceError, AuthenticationError, FileProcessingError,
    QueryProcessingError, AnalyticsError
)

# Import service classes
from .auth_service import AuthService
from .file_service import FileService
from .query_service import QueryService
from .analytics_service import AnalyticsService

# Service instances for dependency injection
auth_service = AuthService()
file_service = FileService()
query_service = QueryService()
analytics_service = AnalyticsService()

# Service registry for easy access
SERVICE_REGISTRY = {
    'auth': auth_service,
    'file': file_service,
    'query': query_service,
    'analytics': analytics_service
}

def get_service(service_name):
    """
    Get a service instance by name.
    
    Args:
        service_name: Name of the service ('auth', 'file', 'query', 'analytics')
        
    Returns:
        Service instance or None if not found
    """
    return SERVICE_REGISTRY.get(service_name)

def initialize_services():
    """
    Initialize all services with required dependencies.
    
    This function should be called during application startup
    to ensure all services are properly configured.
    """
    try:
        # Initialize each service
        auth_service.initialize()
        file_service.initialize()
        query_service.initialize()
        analytics_service.initialize()
        
        return True
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error initializing services: {str(e)}")
        return False


