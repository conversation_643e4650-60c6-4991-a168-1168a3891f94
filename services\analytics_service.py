"""
Analytics Service

Handles all analytics-related business logic including:
- Analytics data collection and processing
- Chat history management
- Session analytics
- Device and client analytics
- Geographic analytics
- Performance metrics
- Greeting analytics

This service extracts analytics business logic from route handlers
to improve testability and maintainability.
"""

import logging
from datetime import datetime, timedelta
import db_utils
import geo_utils
import geoip_analytics
from greeting_manager import GreetingManager
from .exceptions import ServiceError, AnalyticsError
from services.cache_service import get_cache_service, cached

# Configure logging
logger = logging.getLogger(__name__)

class AnalyticsService:
    """Service class for analytics business logic."""
    
    def __init__(self):
        """Initialize the analytics service."""
        self.initialized = False
        self.greeting_manager = None
    
    def initialize(self):
        """Initialize the service with required dependencies."""
        try:
            # Initialize greeting manager for Phase 2 analytics
            self.greeting_manager = GreetingManager()
            
            self.initialized = True
            logger.info("Analytics service initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Error initializing analytics service: {str(e)}")
            return False
    
    @cached(ttl=600)  # Cache for 10 minutes
    def get_analytics_dashboard_data(self, start_date=None, end_date=None):
        """
        Get comprehensive analytics data for the dashboard.

        Args:
            start_date: Optional start date filter
            end_date: Optional end date filter

        Returns:
            Dictionary with comprehensive analytics data
        """
        try:
            # Get basic analytics data
            analytics = db_utils.get_analytics(start_date=start_date, end_date=end_date)
            
            # Get summary statistics
            summary = db_utils.get_analytics_summary()
            
            # Get greeting analytics for Phase 2
            greeting_analytics = {}
            engagement_patterns = {}
            if self.greeting_manager:
                greeting_analytics = self.greeting_manager.get_greeting_analytics(
                    start_date=start_date, end_date=end_date
                )
                engagement_patterns = self.greeting_manager.get_time_based_engagement_patterns()
            
            # Get processing time data for charts
            processing_time_data = db_utils.get_processing_time_data(
                start_date=start_date, end_date=end_date
            )
            
            # Get query volume data
            query_volume_data = db_utils.get_query_volume_data(
                start_date=start_date, end_date=end_date
            )
            
            # Get location data for geographic visualization
            location_data = geo_utils.get_location_analytics_data(
                start_date=start_date, end_date=end_date
            )
            
            # Get model performance data
            model_performance_data = db_utils.get_model_performance_data(
                start_date=start_date, end_date=end_date
            )
            
            # Get development location for filtering
            dev_location = geoip_analytics.get_dev_location()
            
            dashboard_data = {
                'analytics': analytics,
                'summary': summary,
                'greeting_analytics': greeting_analytics,
                'engagement_patterns': engagement_patterns,
                'processing_time_data': processing_time_data,
                'query_volume_data': query_volume_data,
                'location_data': location_data,
                'model_performance_data': model_performance_data,
                'dev_location': dev_location,
                'date_range': {
                    'start_date': start_date,
                    'end_date': end_date
                }
            }
            
            logger.debug("Retrieved comprehensive analytics dashboard data")
            return dashboard_data
            
        except Exception as e:
            logger.error(f"Error getting analytics dashboard data: {str(e)}")
            raise AnalyticsError(f"Failed to get analytics data: {str(e)}")
    
    def get_chat_history(self, session_id=None, limit=None):
        """
        Get chat history with optional filtering.
        
        Args:
            session_id: Optional session ID filter
            limit: Optional limit on number of records
            
        Returns:
            List of chat history records
        """
        try:
            history = db_utils.get_chat_history(session_id=session_id, limit=limit)
            logger.debug(f"Retrieved {len(history)} chat history records")
            return history
        except Exception as e:
            logger.error(f"Error getting chat history: {str(e)}")
            return []
    
    @cached(ttl=300)  # Cache for 5 minutes
    def get_session_analytics(self):
        """
        Get session analytics data.

        Returns:
            Dictionary with session analytics
        """
        try:
            sessions = db_utils.get_all_sessions()
            
            # Process session data for analytics
            session_analytics = {
                'total_sessions': len(sessions),
                'active_sessions': len([s for s in sessions if not s.get('end_time')]),
                'average_duration': self._calculate_average_session_duration(sessions),
                'sessions_by_date': self._group_sessions_by_date(sessions),
                'top_clients': self._get_top_clients(sessions)
            }
            
            logger.debug("Retrieved session analytics data")
            return session_analytics
            
        except Exception as e:
            logger.error(f"Error getting session analytics: {str(e)}")
            return {}
    
    def get_device_analytics(self, device_fingerprint=None, client_name=None):
        """
        Get analytics for a specific device or client.
        
        Args:
            device_fingerprint: Optional device fingerprint filter
            client_name: Optional client name filter
            
        Returns:
            Dictionary with device analytics data
        """
        try:
            # Get device analytics data
            device_data = db_utils.get_device_analytics(
                device_fingerprint=device_fingerprint,
                client_name=client_name
            )
            
            # Get session history for this device
            session_history = db_utils.get_device_session_history(
                device_fingerprint=device_fingerprint,
                client_name=client_name
            )
            
            # Get usage patterns
            usage_patterns = db_utils.get_device_usage_patterns(
                device_fingerprint=device_fingerprint,
                client_name=client_name
            )
            
            analytics_data = {
                'device_data': device_data,
                'session_history': session_history,
                'usage_patterns': usage_patterns,
                'device_fingerprint': device_fingerprint,
                'client_name': client_name
            }
            
            logger.debug(f"Retrieved device analytics for {device_fingerprint or client_name}")
            return analytics_data
            
        except Exception as e:
            logger.error(f"Error getting device analytics: {str(e)}")
            return {}
    
    @cached(ttl=900)  # Cache for 15 minutes
    def get_location_analytics(self):
        """
        Get geographical location analytics.

        Returns:
            Dictionary with location analytics data
        """
        try:
            # Get all extracted locations
            locations = db_utils.get_all_extracted_locations(include_sources=True)
            
            # Get location statistics
            statistics = db_utils.get_location_statistics()
            
            location_analytics = {
                'locations': locations,
                'statistics': statistics,
                'total_locations': len(locations)
            }
            
            logger.debug(f"Retrieved location analytics for {len(locations)} locations")
            return location_analytics
            
        except Exception as e:
            logger.error(f"Error getting location analytics: {str(e)}")
            return {}
    
    def get_model_performance_analysis(self, start_date=None, end_date=None):
        """
        Get model performance analysis data.
        
        Args:
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            Dictionary with model performance data
        """
        try:
            performance_data = db_utils.get_model_performance_data(
                start_date=start_date,
                end_date=end_date
            )
            
            # Process performance data for analysis
            analysis = {
                'performance_data': performance_data,
                'model_comparison': self._analyze_model_performance(performance_data),
                'trends': self._analyze_performance_trends(performance_data),
                'recommendations': self._generate_performance_recommendations(performance_data)
            }
            
            logger.debug("Retrieved model performance analysis")
            return analysis
            
        except Exception as e:
            logger.error(f"Error getting model performance analysis: {str(e)}")
            return {}
    
    def close_session(self, session_id):
        """
        Close a specific session.
        
        Args:
            session_id: The session ID to close
            
        Returns:
            Tuple of (success, message)
        """
        try:
            success = db_utils.close_session(session_id)
            
            if success:
                logger.info(f"Session {session_id} closed successfully")
                return True, "Session closed successfully"
            else:
                logger.warning(f"Failed to close session {session_id}")
                return False, "Failed to close session"
                
        except Exception as e:
            logger.error(f"Error closing session {session_id}: {str(e)}")
            return False, f"Error closing session: {str(e)}"
    
    def _calculate_average_session_duration(self, sessions):
        """Calculate average session duration."""
        try:
            durations = []
            for session in sessions:
                start_time = session.get('start_time')
                end_time = session.get('end_time')
                
                if start_time and end_time:
                    start = datetime.fromisoformat(start_time)
                    end = datetime.fromisoformat(end_time)
                    duration = (end - start).total_seconds()
                    durations.append(duration)
            
            return sum(durations) / len(durations) if durations else 0
            
        except Exception as e:
            logger.error(f"Error calculating average session duration: {str(e)}")
            return 0
    
    def _group_sessions_by_date(self, sessions):
        """Group sessions by date for trend analysis."""
        try:
            sessions_by_date = {}
            
            for session in sessions:
                start_time = session.get('start_time')
                if start_time:
                    date = datetime.fromisoformat(start_time).date().isoformat()
                    sessions_by_date[date] = sessions_by_date.get(date, 0) + 1
            
            return sessions_by_date
            
        except Exception as e:
            logger.error(f"Error grouping sessions by date: {str(e)}")
            return {}
    
    def _get_top_clients(self, sessions, limit=10):
        """Get top clients by session count."""
        try:
            client_counts = {}
            
            for session in sessions:
                client_name = session.get('client_name', 'Anonymous')
                client_counts[client_name] = client_counts.get(client_name, 0) + 1
            
            # Sort by count and return top clients
            top_clients = sorted(client_counts.items(), key=lambda x: x[1], reverse=True)
            return top_clients[:limit]
            
        except Exception as e:
            logger.error(f"Error getting top clients: {str(e)}")
            return []
    
    def _analyze_model_performance(self, performance_data):
        """Analyze model performance for comparison."""
        try:
            # Implement model performance comparison logic
            # This is a placeholder for more sophisticated analysis
            return {
                'best_performing_model': 'Analysis pending',
                'performance_metrics': {},
                'comparison_data': []
            }
        except Exception as e:
            logger.error(f"Error analyzing model performance: {str(e)}")
            return {}
    
    def _analyze_performance_trends(self, performance_data):
        """Analyze performance trends over time."""
        try:
            # Implement trend analysis logic
            # This is a placeholder for more sophisticated analysis
            return {
                'trend_direction': 'stable',
                'trend_data': [],
                'insights': []
            }
        except Exception as e:
            logger.error(f"Error analyzing performance trends: {str(e)}")
            return {}
    
    def _generate_performance_recommendations(self, performance_data):
        """Generate performance improvement recommendations."""
        try:
            # Implement recommendation logic
            # This is a placeholder for more sophisticated analysis
            return [
                'Monitor response times regularly',
                'Consider model optimization for better performance',
                'Review query patterns for optimization opportunities'
            ]
        except Exception as e:
            logger.error(f"Error generating performance recommendations: {str(e)}")
            return []
