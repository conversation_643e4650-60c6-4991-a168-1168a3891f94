"""
Repository Pattern Implementation

This package provides repository interfaces and implementations for data access,
following the repository pattern for clean architecture and testability.

Features:
- Abstract repository interfaces
- Concrete implementations for different data sources
- Unit of work pattern support
- Query specification pattern
- Caching integration
- Transaction management

Version: 1.0.0
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, TypeVar, Generic, Union
from dataclasses import dataclass
from datetime import datetime

T = TypeVar('T')
TKey = TypeVar('TKey')

@dataclass
class QuerySpecification:
    """Specification for repository queries"""
    filters: Dict[str, Any] = None
    order_by: List[str] = None
    limit: Optional[int] = None
    offset: Optional[int] = None
    include_deleted: bool = False
    
    def __post_init__(self):
        if self.filters is None:
            self.filters = {}
        if self.order_by is None:
            self.order_by = []

@dataclass
class PagedResult(Generic[T]):
    """Result of a paged query"""
    items: List[T]
    total_count: int
    page_size: int
    page_number: int
    has_next: bool
    has_previous: bool
    
    @property
    def total_pages(self) -> int:
        return (self.total_count + self.page_size - 1) // self.page_size

class IRepository(ABC, Generic[T, TKey]):
    """Base repository interface"""
    
    @abstractmethod
    async def get_by_id(self, id: TKey) -> Optional[T]:
        """Get entity by ID"""
        pass
    
    @abstractmethod
    async def get_all(self, spec: Optional[QuerySpecification] = None) -> List[T]:
        """Get all entities matching specification"""
        pass
    
    @abstractmethod
    async def get_paged(self, page: int, page_size: int, spec: Optional[QuerySpecification] = None) -> PagedResult[T]:
        """Get paged results"""
        pass
    
    @abstractmethod
    async def add(self, entity: T) -> T:
        """Add new entity"""
        pass
    
    @abstractmethod
    async def update(self, entity: T) -> T:
        """Update existing entity"""
        pass
    
    @abstractmethod
    async def delete(self, id: TKey) -> bool:
        """Delete entity by ID"""
        pass
    
    @abstractmethod
    async def exists(self, id: TKey) -> bool:
        """Check if entity exists"""
        pass
    
    @abstractmethod
    async def count(self, spec: Optional[QuerySpecification] = None) -> int:
        """Count entities matching specification"""
        pass

class IDocumentRepository(IRepository[Dict[str, Any], str]):
    """Repository interface for PDF documents"""
    
    @abstractmethod
    async def get_by_category(self, category: str) -> List[Dict[str, Any]]:
        """Get documents by category"""
        pass
    
    @abstractmethod
    async def get_by_filename(self, filename: str) -> Optional[Dict[str, Any]]:
        """Get document by filename"""
        pass
    
    @abstractmethod
    async def search_by_content(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search documents by content"""
        pass
    
    @abstractmethod
    async def get_categories(self) -> List[str]:
        """Get all available categories"""
        pass

class IAnalyticsRepository(IRepository[Dict[str, Any], str]):
    """Repository interface for analytics data"""
    
    @abstractmethod
    async def get_query_analytics(self, start_date: Optional[datetime] = None, 
                                 end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Get query analytics data"""
        pass
    
    @abstractmethod
    async def get_session_analytics(self, start_date: Optional[datetime] = None,
                                   end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Get session analytics data"""
        pass
    
    @abstractmethod
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        pass
    
    @abstractmethod
    async def record_query(self, query_data: Dict[str, Any]) -> bool:
        """Record a query for analytics"""
        pass

class IUserRepository(IRepository[Dict[str, Any], str]):
    """Repository interface for user data"""
    
    @abstractmethod
    async def get_by_device_fingerprint(self, fingerprint: str) -> Optional[Dict[str, Any]]:
        """Get user by device fingerprint"""
        pass
    
    @abstractmethod
    async def get_by_client_name(self, client_name: str) -> List[Dict[str, Any]]:
        """Get users by client name"""
        pass
    
    @abstractmethod
    async def update_last_activity(self, user_id: str, activity_time: datetime) -> bool:
        """Update user's last activity time"""
        pass

class IUnitOfWork(ABC):
    """Unit of work interface for transaction management"""
    
    @property
    @abstractmethod
    def documents(self) -> IDocumentRepository:
        """Document repository"""
        pass
    
    @property
    @abstractmethod
    def analytics(self) -> IAnalyticsRepository:
        """Analytics repository"""
        pass
    
    @property
    @abstractmethod
    def users(self) -> IUserRepository:
        """User repository"""
        pass
    
    @abstractmethod
    async def begin_transaction(self):
        """Begin a transaction"""
        pass
    
    @abstractmethod
    async def commit(self):
        """Commit the transaction"""
        pass
    
    @abstractmethod
    async def rollback(self):
        """Rollback the transaction"""
        pass
    
    @abstractmethod
    async def save_changes(self) -> int:
        """Save all changes and return number of affected records"""
        pass

# Export main interfaces
__all__ = [
    'IRepository',
    'IDocumentRepository', 
    'IAnalyticsRepository',
    'IUserRepository',
    'IUnitOfWork',
    'QuerySpecification',
    'PagedResult'
]
