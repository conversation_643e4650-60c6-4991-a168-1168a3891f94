"""
Image Optimizer Service

This service provides advanced image compression, lazy loading, and optimization features
to reduce bandwidth usage and improve page load times.

Features:
- Progressive JPEG compression
- WebP format conversion
- Responsive image generation
- Lazy loading support
- Image caching and CDN optimization
- Bandwidth-aware compression
- Real-time image optimization

Version: 1.0.0
"""

import os
import io
import logging
import hashlib
import tempfile
from typing import Optional, Dict, Any, Tuple, List
from dataclasses import dataclass
from PIL import Image, ImageOps, ImageFilter
import base64
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class ImageOptimizationConfig:
    """Configuration for image optimization"""
    max_width: int = 1920
    max_height: int = 1080
    jpeg_quality: int = 85
    webp_quality: int = 80
    progressive_jpeg: bool = True
    strip_metadata: bool = True
    enable_webp: bool = True
    lazy_loading: bool = True
    responsive_sizes: List[int] = None
    
    def __post_init__(self):
        if self.responsive_sizes is None:
            self.responsive_sizes = [320, 640, 1024, 1920]

@dataclass
class OptimizationResult:
    """Result of image optimization"""
    original_size: int
    optimized_size: int
    compression_ratio: float
    format_used: str
    processing_time: float
    responsive_variants: Dict[str, str] = None
    
    @property
    def size_reduction_mb(self) -> float:
        return (self.original_size - self.optimized_size) / (1024 * 1024)
    
    @property
    def compression_percentage(self) -> float:
        return (1 - self.optimized_size / self.original_size) * 100

class ImageOptimizer:
    """Advanced image optimization service"""
    
    def __init__(self, config: Optional[ImageOptimizationConfig] = None):
        """
        Initialize the image optimizer.
        
        Args:
            config: Optimization configuration
        """
        self.config = config or ImageOptimizationConfig()
        self.cache_dir = os.path.join(os.getenv("TEMP_FOLDER", "./_temp"), "image_cache")
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Statistics tracking
        self.stats = {
            'images_processed': 0,
            'total_size_saved_mb': 0.0,
            'total_processing_time': 0.0,
            'webp_conversions': 0,
            'responsive_variants_created': 0
        }
    
    def _get_cache_key(self, image_path: str, config_hash: str) -> str:
        """Generate cache key for optimized image"""
        path_hash = hashlib.md5(image_path.encode()).hexdigest()
        return f"{path_hash}_{config_hash}"
    
    def _get_config_hash(self) -> str:
        """Generate hash of current configuration"""
        config_str = f"{self.config.max_width}_{self.config.max_height}_{self.config.jpeg_quality}_{self.config.webp_quality}"
        return hashlib.md5(config_str.encode()).hexdigest()[:8]
    
    def optimize_image(self, image_path: str, output_format: str = 'auto') -> Optional[OptimizationResult]:
        """
        Optimize a single image with compression and format conversion.
        
        Args:
            image_path: Path to the input image
            output_format: Output format ('jpeg', 'webp', 'auto')
            
        Returns:
            OptimizationResult with optimization details
        """
        import time
        start_time = time.time()
        
        try:
            # Check cache first
            config_hash = self._get_config_hash()
            cache_key = self._get_cache_key(image_path, config_hash)
            
            original_size = os.path.getsize(image_path)
            
            # Open and analyze image
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # Get original dimensions
                original_width, original_height = img.size
                
                # Calculate new dimensions
                new_width, new_height = self._calculate_dimensions(
                    original_width, original_height
                )
                
                # Resize if needed
                if new_width != original_width or new_height != original_height:
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # Strip metadata if configured
                if self.config.strip_metadata:
                    img = ImageOps.exif_transpose(img)
                
                # Determine output format
                if output_format == 'auto':
                    output_format = 'webp' if self.config.enable_webp else 'jpeg'
                
                # Create optimized image
                optimized_data = self._compress_image(img, output_format)
                
                # Calculate results
                optimized_size = len(optimized_data)
                compression_ratio = optimized_size / original_size
                processing_time = time.time() - start_time
                
                # Update statistics
                self.stats['images_processed'] += 1
                self.stats['total_size_saved_mb'] += (original_size - optimized_size) / (1024 * 1024)
                self.stats['total_processing_time'] += processing_time
                
                if output_format == 'webp':
                    self.stats['webp_conversions'] += 1
                
                return OptimizationResult(
                    original_size=original_size,
                    optimized_size=optimized_size,
                    compression_ratio=compression_ratio,
                    format_used=output_format,
                    processing_time=processing_time
                )
                
        except Exception as e:
            logger.error(f"Error optimizing image {image_path}: {str(e)}")
            return None
    
    def _calculate_dimensions(self, width: int, height: int) -> Tuple[int, int]:
        """Calculate new dimensions while maintaining aspect ratio"""
        if width <= self.config.max_width and height <= self.config.max_height:
            return width, height
        
        # Calculate scaling factor
        width_scale = self.config.max_width / width
        height_scale = self.config.max_height / height
        scale = min(width_scale, height_scale)
        
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        return new_width, new_height
    
    def _compress_image(self, img: Image.Image, format_type: str) -> bytes:
        """Compress image to specified format"""
        buffer = io.BytesIO()
        
        if format_type.lower() == 'webp':
            img.save(
                buffer,
                format='WebP',
                quality=self.config.webp_quality,
                optimize=True
            )
        else:  # JPEG
            img.save(
                buffer,
                format='JPEG',
                quality=self.config.jpeg_quality,
                optimize=True,
                progressive=self.config.progressive_jpeg
            )
        
        return buffer.getvalue()
    
    def create_responsive_variants(self, image_path: str) -> Dict[str, str]:
        """
        Create responsive image variants for different screen sizes.
        
        Args:
            image_path: Path to the input image
            
        Returns:
            Dictionary mapping size to optimized image path
        """
        variants = {}
        
        try:
            with Image.open(image_path) as img:
                original_width, original_height = img.size
                
                for size in self.config.responsive_sizes:
                    if size >= original_width:
                        continue  # Skip if size is larger than original
                    
                    # Calculate proportional height
                    aspect_ratio = original_height / original_width
                    new_height = int(size * aspect_ratio)
                    
                    # Create variant
                    variant_img = img.resize((size, new_height), Image.Resampling.LANCZOS)
                    
                    # Save variant
                    variant_filename = f"{Path(image_path).stem}_{size}w.jpg"
                    variant_path = os.path.join(self.cache_dir, variant_filename)
                    
                    variant_img.save(
                        variant_path,
                        format='JPEG',
                        quality=self.config.jpeg_quality,
                        optimize=True
                    )
                    
                    variants[f"{size}w"] = variant_path
                    self.stats['responsive_variants_created'] += 1
            
            return variants
            
        except Exception as e:
            logger.error(f"Error creating responsive variants for {image_path}: {str(e)}")
            return {}
    
    def generate_lazy_loading_placeholder(self, image_path: str, blur_radius: int = 10) -> str:
        """
        Generate a low-quality placeholder for lazy loading.
        
        Args:
            image_path: Path to the input image
            blur_radius: Blur radius for placeholder
            
        Returns:
            Base64 encoded placeholder image
        """
        try:
            with Image.open(image_path) as img:
                # Create small placeholder (32x24 or proportional)
                placeholder_width = 32
                aspect_ratio = img.height / img.width
                placeholder_height = int(placeholder_width * aspect_ratio)
                
                # Resize and blur
                placeholder = img.resize(
                    (placeholder_width, placeholder_height),
                    Image.Resampling.LANCZOS
                )
                placeholder = placeholder.filter(Image.ImageFilter.GaussianBlur(blur_radius))
                
                # Convert to base64
                buffer = io.BytesIO()
                placeholder.save(buffer, format='JPEG', quality=50)
                
                return base64.b64encode(buffer.getvalue()).decode('utf-8')
                
        except Exception as e:
            logger.error(f"Error generating placeholder for {image_path}: {str(e)}")
            return ""
    
    def optimize_for_web(self, image_path: str, create_variants: bool = True) -> Dict[str, Any]:
        """
        Comprehensive web optimization including compression, variants, and placeholders.
        
        Args:
            image_path: Path to the input image
            create_variants: Whether to create responsive variants
            
        Returns:
            Dictionary with all optimization results
        """
        result = {
            'original_path': image_path,
            'optimized': None,
            'variants': {},
            'placeholder': '',
            'lazy_loading_enabled': self.config.lazy_loading
        }
        
        # Main optimization
        optimization_result = self.optimize_image(image_path)
        if optimization_result:
            result['optimized'] = optimization_result
        
        # Create responsive variants
        if create_variants:
            variants = self.create_responsive_variants(image_path)
            result['variants'] = variants
        
        # Generate lazy loading placeholder
        if self.config.lazy_loading:
            placeholder = self.generate_lazy_loading_placeholder(image_path)
            result['placeholder'] = placeholder
        
        return result
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """Get comprehensive optimization statistics"""
        avg_processing_time = (
            self.stats['total_processing_time'] / self.stats['images_processed']
            if self.stats['images_processed'] > 0 else 0
        )
        
        return {
            'images_processed': self.stats['images_processed'],
            'total_size_saved_mb': round(self.stats['total_size_saved_mb'], 2),
            'avg_processing_time_ms': round(avg_processing_time * 1000, 2),
            'webp_conversions': self.stats['webp_conversions'],
            'responsive_variants_created': self.stats['responsive_variants_created'],
            'webp_adoption_rate': (
                self.stats['webp_conversions'] / self.stats['images_processed'] * 100
                if self.stats['images_processed'] > 0 else 0
            )
        }

# Global optimizer instance
_image_optimizer: Optional[ImageOptimizer] = None

def get_image_optimizer() -> ImageOptimizer:
    """Get global image optimizer instance"""
    global _image_optimizer
    if _image_optimizer is None:
        # Load configuration from environment
        config = ImageOptimizationConfig(
            max_width=int(os.getenv('IMAGE_MAX_WIDTH', '1920')),
            max_height=int(os.getenv('IMAGE_MAX_HEIGHT', '1080')),
            jpeg_quality=int(os.getenv('IMAGE_JPEG_QUALITY', '85')),
            webp_quality=int(os.getenv('IMAGE_WEBP_QUALITY', '80')),
            enable_webp=os.getenv('IMAGE_ENABLE_WEBP', 'true').lower() == 'true',
            lazy_loading=os.getenv('IMAGE_LAZY_LOADING', 'true').lower() == 'true'
        )
        
        _image_optimizer = ImageOptimizer(config)
    
    return _image_optimizer
