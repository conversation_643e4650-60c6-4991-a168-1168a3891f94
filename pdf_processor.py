import os
import logging
import json
import fitz  # PyMuPDF
import uuid
import base64
import numpy as np
from pathlib import Path
from datetime import datetime
from werkzeug.utils import secure_filename
from langchain.schema import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
import shutil
from services.file_io_optimizer import get_file_io_optimizer

# Import vision processor for image analysis
try:
    import vision_processor
    HAS_VISION = True
except ImportError:
    HAS_VISION = False
    logging.warning("Vision processor module not available. Image analysis features will be disabled.")

# Try to import optional dependencies
try:
    import cv2
    import pytesseract
    from PIL import Image
    HAS_OCR = True
    HAS_OPENCV = True
except ImportError:
    HAS_OCR = False
    HAS_OPENCV = False
    logging.warning("OCR dependencies (pytesseract, opencv-python, pillow) not available. OCR features will be disabled.")

try:
    import tabula
    HAS_TABULA = True
except ImportError:
    HAS_TABULA = False
    logging.warning("tabula-py not available. Table extraction with tabula will be disabled.")

try:
    import camelot
    HAS_CAMELOT = True
except ImportError:
    HAS_CAMELOT = False
    logging.warning("camelot-py not available. Table extraction with camelot will be disabled.")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./_temp")
GHOSTSCRIPT_PATH = os.getenv("GHOSTSCRIPT_PATH", "D:/Program Files/gs/gs10.05.1/bin/gswin64c.exe")

# Import the directory creation function
from create_temp_dirs import create_pdf_directory_structure

# Set Ghostscript path for pdf2image if it exists
if os.path.exists(GHOSTSCRIPT_PATH):
    os.environ["GHOSTSCRIPT_BINARY"] = GHOSTSCRIPT_PATH
else:
    logger.warning(f"Ghostscript not found at {GHOSTSCRIPT_PATH}. PDF to image conversion may be limited.")

def extract_text_standard(pdf_path):
    """Extract text from standard PDF using PyMuPDF."""
    text_by_page = []
    try:
        doc = fitz.open(pdf_path)
        for page_num, page in enumerate(doc):
            text = page.get_text()
            if text.strip():
                text_by_page.append({
                    "page": page_num + 1,
                    "text": text,
                    "extraction_method": "standard"
                })
        return text_by_page
    except Exception as e:
        logger.error(f"Failed to extract standard text from PDF {pdf_path}: {str(e)}")
        return []

def extract_text_standard_streaming(pdf_path):
    """Extract text from standard PDF using streaming for memory efficiency."""
    optimizer = get_file_io_optimizer()

    def page_processor(page, page_num):
        """Process individual page for text extraction"""
        try:
            text = page.get_text()
            if text.strip():
                return {
                    "text": {
                        "page": page_num + 1,
                        "text": text,
                        "extraction_method": "standard_streaming"
                    }
                }
        except Exception as e:
            logger.error(f"Error extracting text from page {page_num + 1}: {str(e)}")
        return None

    try:
        # Use streaming processor for large files
        file_size_mb = optimizer.get_file_size_mb(pdf_path)

        if file_size_mb > 25:  # Use streaming for files larger than 25MB
            logger.info(f"Using streaming text extraction for large PDF ({file_size_mb:.2f}MB): {pdf_path}")
            result = optimizer.process_pdf_streaming(pdf_path, page_processor)
            return result.get('text_extracted', [])
        else:
            # Use standard extraction for smaller files
            return extract_text_standard(pdf_path)

    except Exception as e:
        logger.error(f"Failed to extract text with streaming from PDF {pdf_path}: {str(e)}")
        return []

def extract_text_with_ocr(pdf_path):
    """Extract text from scanned PDF pages using Tesseract OCR."""
    text_by_page = []

    # Check if OCR dependencies are available
    if not HAS_OCR or not HAS_OPENCV:
        logger.warning("OCR dependencies not available. Skipping OCR text extraction.")
        return text_by_page

    try:
        # Open the PDF
        doc = fitz.open(pdf_path)

        for page_num, page in enumerate(doc):
            # Convert page to image
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom for better OCR
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

            # Convert PIL image to OpenCV format
            img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

            # Preprocess image for better OCR
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
            gray = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)[1]

            # Apply OCR
            text = pytesseract.image_to_string(gray)

            if text.strip():
                text_by_page.append({
                    "page": page_num + 1,
                    "text": text,
                    "extraction_method": "ocr"
                })

        return text_by_page
    except Exception as e:
        logger.error(f"Failed to extract text with OCR from PDF {pdf_path}: {str(e)}")
        return []

def extract_images_from_pdf_streaming(pdf_path, category=None, save_images=True, use_vision=None, filter_sensitivity=None, max_images=None):
    """
    Extract and optionally save images from PDF using streaming for memory efficiency.

    This function uses the file I/O optimizer for large PDFs to minimize memory usage.
    """
    optimizer = get_file_io_optimizer()
    file_size_mb = optimizer.get_file_size_mb(pdf_path)

    # For large files, use streaming approach
    if file_size_mb > 50:  # Use streaming for files larger than 50MB
        logger.info(f"Using streaming image extraction for large PDF ({file_size_mb:.2f}MB): {pdf_path}")
        return _extract_images_streaming_impl(pdf_path, category, save_images, use_vision, filter_sensitivity, max_images)
    else:
        # Use standard extraction for smaller files
        return extract_images_from_pdf(pdf_path, category, save_images, use_vision, filter_sensitivity, max_images)

def _extract_images_streaming_impl(pdf_path, category=None, save_images=True, use_vision=None, filter_sensitivity=None, max_images=None):
    """Implementation of streaming image extraction"""
    optimizer = get_file_io_optimizer()

    # Initialize tracking variables
    all_images = []
    images_processed = 0
    max_images = max_images or 30

    def page_processor(page, page_num):
        """Process individual page for image extraction"""
        nonlocal images_processed

        if images_processed >= max_images:
            return None

        try:
            page_images = []
            image_list = page.get_images(full=True)

            for img_index, img in enumerate(image_list):
                if images_processed >= max_images:
                    break

                # Extract image with memory optimization
                xref = img[0]
                base_image = page.parent.extract_image(xref)
                image_bytes = base_image["image"]
                image_ext = base_image["ext"]

                # Process image with optimizer
                if save_images and category:
                    # Save image using streaming
                    image_info = _save_image_streaming(
                        image_bytes, image_ext, category, pdf_path,
                        page_num, img_index, use_vision, filter_sensitivity
                    )
                    if image_info:
                        page_images.append(image_info)
                        images_processed += 1

                # Force cleanup for large images
                if len(image_bytes) > 1024 * 1024:  # 1MB
                    del image_bytes
                    del base_image

            return {"images": page_images} if page_images else None

        except Exception as e:
            logger.error(f"Error extracting images from page {page_num + 1}: {str(e)}")
            return None

    try:
        result = optimizer.process_pdf_streaming(pdf_path, page_processor)
        return result.get('images_extracted', [])

    except Exception as e:
        logger.error(f"Failed to extract images with streaming from PDF {pdf_path}: {str(e)}")
        return []

def _save_image_streaming(image_bytes, image_ext, category, pdf_path, page_num, img_index, use_vision, filter_sensitivity):
    """Save image using streaming optimization"""
    try:
        optimizer = get_file_io_optimizer()

        # Create temporary file for image processing
        import tempfile
        with tempfile.NamedTemporaryFile(suffix=f'.{image_ext}', delete=False) as temp_file:
            temp_file.write(image_bytes)
            temp_path = temp_file.name

        # Optimize image if it's large
        if len(image_bytes) > 512 * 1024:  # 512KB
            optimized_path = optimizer.optimize_image_streaming(temp_path)
            if optimized_path:
                # Use optimized image
                with open(optimized_path, 'rb') as f:
                    image_bytes = f.read()
                os.unlink(temp_path)
                temp_path = optimized_path

        # Continue with standard image saving logic
        # (This would integrate with existing image saving code)

        # Cleanup
        if os.path.exists(temp_path):
            os.unlink(temp_path)

        return {
            "page": page_num + 1,
            "index": img_index,
            "size": len(image_bytes),
            "format": image_ext,
            "optimized": True
        }

    except Exception as e:
        logger.error(f"Error saving image with streaming: {str(e)}")
        return None

def extract_images_from_pdf(pdf_path, category=None, save_images=True, use_vision=None, filter_sensitivity=None, max_images=None):
    """
    Extract and optionally save images from PDF using PyMuPDF with vision model analysis.

    This function extracts images from PDFs and saves them in the hierarchical directory structure
    (_temp/CATEGORY/PDF_NAME/pdf_images/) when category is provided and save_images is True.

    Args:
        pdf_path: Path to the PDF file
        category: Category for organizing content
        save_images: Whether to save extracted images to disk (set to False to only extract metadata)
        use_vision: Whether to use vision model for image analysis (defaults to environment variable)
        filter_sensitivity: Sensitivity level for filtering images (low, medium, high)
        max_images: Maximum number of images to save

    Returns:
        List of image info dictionaries
    """
    images = []
    filtered_images = []

    # Get vision settings from environment variables if not provided
    if use_vision is None:
        use_vision = os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'true').lower() == 'true'

    if filter_sensitivity is None:
        filter_sensitivity = os.getenv('PDF_IMAGE_FILTER_SENSITIVITY', 'medium')

    if max_images is None:
        try:
            max_images = int(os.getenv('MAX_PDF_IMAGES_TO_ANALYZE', '10'))
        except (ValueError, TypeError):
            max_images = 10

    # Set relevance threshold based on sensitivity
    if filter_sensitivity == 'low':
        relevance_threshold = 3  # More permissive
    elif filter_sensitivity == 'high':
        relevance_threshold = 7  # More strict
    else:  # medium (default)
        relevance_threshold = 5

    try:
        # Get the PDF filename
        pdf_name = os.path.basename(pdf_path)
        pdf_base_name = os.path.splitext(pdf_name)[0]

        # Determine the image directory path
        if save_images:
            if category:
                # Use the hierarchical directory structure (preferred method)
                dir_structure = create_pdf_directory_structure(category, pdf_name)
                if not dir_structure:
                    logger.error(f"Failed to create directory structure for {pdf_name}")
                    return []

                # Use the PDF-specific images directory
                image_dir = dir_structure["pdf_images_dir"]
            else:
                # Only use temp_images as a fallback if no category is provided
                # This should be rare in production use
                image_dir = os.path.join(TEMP_FOLDER, "temp_images")
                os.makedirs(image_dir, exist_ok=True)
                logger.warning(f"No category provided for PDF {pdf_name}, using temporary directory for images")

        # Open the PDF
        doc = fitz.open(pdf_path)

        # Track total images found for analytics
        total_images_found = 0
        images_saved = 0

        for page_num, page in enumerate(doc):
            image_list = page.get_images(full=True)

            for img_index, img in enumerate(image_list):
                total_images_found += 1

                xref = img[0]
                base_image = doc.extract_image(xref)
                image_bytes = base_image["image"]
                image_ext = base_image["ext"]

                # Create image info dictionary
                image_info = {
                    "page": page_num + 1,
                    "index": img_index,
                    "size": len(image_bytes),
                    "format": image_ext
                }

                # Process and save the image if requested
                if save_images:
                    # Generate a unique filename
                    image_filename = f"{pdf_base_name}_{page_num+1}_{img_index}.{image_ext}"
                    image_path = os.path.join(image_dir, image_filename)

                    with open(image_path, "wb") as f:
                        f.write(image_bytes)

                    # Add URL for accessing the image through the Flask app
                    if category:
                        image_info["url"] = f"/{category}/{pdf_base_name}/pdf_images/{image_filename}"
                    else:
                        image_info["url"] = f"/temp_images/{image_filename}"

                    # Add a description for better accessibility
                    image_info["description"] = f"Image from page {page_num+1} of {Path(pdf_path).name}"

                    # Analyze image with vision model if enabled and available
                    if use_vision and HAS_VISION:
                        try:
                            # Use the direct file path to avoid duplicate downloads and caching
                            # This prevents the vision processor from creating duplicate cached copies

                            # Store the file path in the image info for direct access
                            image_info["file_path"] = image_path

                            # Analyze the image using the direct file path
                            analysis = vision_processor.detect_image_content(image_path, is_pdf_image=True)

                            if "error" not in analysis:
                                # Add content metadata
                                image_info["is_logo"] = analysis.get("is_logo", False)
                                image_info["category"] = analysis.get("category", "unknown")
                                image_info["objects"] = analysis.get("objects", [])
                                image_info["has_text"] = analysis.get("text") is not None and len(analysis.get("text", "")) > 0
                                image_info["text_content"] = analysis.get("text")
                                image_info["is_decorative"] = analysis.get("is_decorative", False)
                                image_info["relevance_score"] = analysis.get("relevance_score", 5)

                                # Use the AI-generated description if available
                                if analysis.get("description"):
                                    image_info["description"] = analysis.get("description")

                                # Mark as analyzed
                                image_info["analyzed"] = True

                                # Check if image should be filtered based on relevance score
                                if (image_info.get("relevance_score", 5) < relevance_threshold or
                                    image_info.get("is_logo", False) or
                                    image_info.get("is_decorative", False)):

                                    # Add to filtered images list
                                    filtered_images.append(image_info)

                                    # Skip adding to main images list
                                    continue
                            else:
                                # Store error information
                                image_info["vision_error"] = analysis.get("error")
                                image_info["analyzed"] = False
                        except Exception as e:
                            logger.error(f"Error analyzing PDF image: {str(e)}")
                            image_info["vision_error"] = str(e)
                            image_info["analyzed"] = False

                # Add to images list if it passed filtering or if vision analysis is disabled
                # Always add basic metadata even if save_images is False
                images.append(image_info)
                images_saved += 1

                # Stop processing if we've reached the maximum number of images
                if max_images and images_saved >= max_images:
                    logger.info(f"Reached maximum number of images to save ({max_images})")
                    break

            # Also break outer loop if we've reached the maximum
            if max_images and images_saved >= max_images:
                break

        # Add analytics data to the first image if any images were saved
        if images:
            images[0]["total_images_found"] = total_images_found
            images[0]["images_saved"] = images_saved
            images[0]["images_filtered"] = len(filtered_images)
            images[0]["filter_sensitivity"] = filter_sensitivity
            images[0]["relevance_threshold"] = relevance_threshold

        # Add filtered images metadata for tracking
        if filtered_images:
            # Store filtered images separately for potential review
            filtered_dir = os.path.join(image_dir, "filtered")
            os.makedirs(filtered_dir, exist_ok=True)

            # Save filtered images metadata
            filtered_metadata_path = os.path.join(filtered_dir, "filtered_images.json")
            with open(filtered_metadata_path, "w") as f:
                json.dump(filtered_images, f, indent=2)

            logger.info(f"Filtered out {len(filtered_images)} images based on vision analysis")

        return images
    except Exception as e:
        logger.error(f"Failed to extract images from PDF {pdf_path}: {str(e)}")
        return []

def extract_tables_with_tabula(pdf_path, category=None, save_tables=True):
    """Extract tables from PDF using tabula-py."""
    tables = []

    # Check if tabula is available
    if not HAS_TABULA:
        logger.warning("tabula-py not available. Skipping table extraction with tabula.")
        return tables

    try:
        # Get the PDF filename
        pdf_name = os.path.basename(pdf_path)
        pdf_base_name = os.path.splitext(pdf_name)[0]

        # Determine the table directory path
        if save_tables:
            if category:
                # Use the hierarchical directory structure (preferred method)
                dir_structure = create_pdf_directory_structure(category, pdf_name)
                if not dir_structure:
                    logger.error(f"Failed to create directory structure for {pdf_name}")
                    return []

                # Use the PDF-specific tables directory
                table_dir = dir_structure["pdf_tables_dir"]
            else:
                # Only use temp_tables as a fallback if no category is provided
                # This should be rare in production use
                table_dir = os.path.join(TEMP_FOLDER, "temp_tables")
                os.makedirs(table_dir, exist_ok=True)
                logger.warning(f"No category provided for PDF {pdf_name}, using temporary directory for tables")

        # Extract all tables from the PDF
        extracted_tables = tabula.read_pdf(pdf_path, pages='all', multiple_tables=True)

        if not extracted_tables:
            return []

        for i, table in enumerate(extracted_tables):
            # Convert table to HTML with better styling
            table_html = table.to_html(classes='table table-sm table-bordered table-responsive')

            # Enhance the HTML with better styling
            table_html = table_html.replace('<table', '<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;"')
            table_html = table_html.replace('<th', '<th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"')
            table_html = table_html.replace('<td', '<td style="border: 1px solid #dee2e6; padding: 0.3rem;"')

            # Save table if requested
            if save_tables:
                table_filename = f"{pdf_base_name}_{i+1}.html"
                table_path = os.path.join(table_dir, table_filename)

                with open(table_path, "w", encoding="utf-8") as f:
                    f.write(table_html)

                # Create table info
                table_info = {
                    "index": i + 1,
                    "rows": len(table),
                    "columns": len(table.columns),
                    "html": table_html
                }

                # Add URL for accessing the table through the Flask app
                # The URL format needs to be updated to match the new directory structure
                if category:
                    table_info["url"] = f"/{category}/{pdf_base_name}/pdf_tables/{table_filename}"
                else:
                    table_info["url"] = f"/temp_tables/{table_filename}"

                tables.append(table_info)

        return tables
    except Exception as e:
        logger.error(f"Failed to extract tables with tabula from PDF {pdf_path}: {str(e)}")
        return []

def extract_cover_image_from_pdf(pdf_path, category=None):
    """
    Extract the first page of a PDF as a cover image/thumbnail.

    Args:
        pdf_path: Path to the PDF file
        category: Category for organizing content

    Returns:
        Dictionary containing thumbnail information or None if extraction fails
    """
    try:
        # Get the PDF filename
        pdf_name = os.path.basename(pdf_path)
        pdf_base_name = os.path.splitext(pdf_name)[0]

        # Create directory structure if category is provided
        if category:
            dir_structure = create_pdf_directory_structure(category, pdf_name)
            if not dir_structure:
                logger.error(f"Failed to create directory structure for {pdf_name}")
                return None

            # Create cover_image directory
            pdf_images_dir = dir_structure["pdf_images_dir"]
            cover_image_dir = os.path.join(pdf_images_dir, "cover_image")
            os.makedirs(cover_image_dir, exist_ok=True)

            # Generate thumbnail filename
            thumbnail_filename = f"{pdf_base_name}_thumbnail.jpg"
            thumbnail_path = os.path.join(cover_image_dir, thumbnail_filename)

            # Open the PDF
            doc = fitz.open(pdf_path)

            # Check if PDF has pages
            if doc.page_count == 0:
                logger.warning(f"PDF {pdf_path} has no pages")
                return None

            # Get the first page
            page = doc[0]

            # Render the page to an image (higher resolution for better quality)
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))

            # Save the image
            pix.save(thumbnail_path)

            # Create thumbnail info
            thumbnail_info = {
                "source": "pdf_first_page",
                "path": thumbnail_path,
                "url": f"/{category}/{pdf_base_name}/pdf_images/cover_image/{thumbnail_filename}",
                "filename": thumbnail_filename,
                "description": f"Cover image from {pdf_name}"
            }

            # Log the thumbnail path for debugging
            logger.info(f"Created thumbnail at path: {thumbnail_path}")

            # Log the thumbnail URL for debugging
            logger.info(f"Created thumbnail with URL: {thumbnail_info['url']}")

            logger.info(f"Successfully extracted cover image from {pdf_path}")
            return thumbnail_info
        else:
            logger.warning(f"No category provided for PDF {pdf_name}, skipping cover image extraction")
            return None
    except Exception as e:
        logger.error(f"Failed to extract cover image from PDF {pdf_path}: {str(e)}")
        return None

def extract_links_from_pdf(pdf_path):
    """Extract links from PDF using PyMuPDF."""
    links = []
    try:
        doc = fitz.open(pdf_path)
        for page_num, page in enumerate(doc):
            link_list = page.get_links()
            for link in link_list:
                if "uri" in link:
                    uri = link["uri"]
                    if uri.startswith(("http://", "https://")):
                        link_info = {
                            "url": uri,
                            "page": page_num + 1
                        }
                        links.append(link_info)
        return links
    except Exception as e:
        logger.error(f"Failed to extract links from PDF {pdf_path}: {str(e)}")
        return []

def extract_tables_with_camelot(pdf_path, category=None, save_tables=True):
    """Extract tables from PDF using camelot-py (more accurate than tabula)."""
    tables = []

    # Check if camelot is available
    if not HAS_CAMELOT:
        logger.warning("camelot-py not available. Skipping table extraction with camelot.")
        return tables

    try:
        # Get the PDF filename
        pdf_name = os.path.basename(pdf_path)
        pdf_base_name = os.path.splitext(pdf_name)[0]

        # Determine the table directory path
        if save_tables:
            if category:
                # Use the hierarchical directory structure (preferred method)
                dir_structure = create_pdf_directory_structure(category, pdf_name)
                if not dir_structure:
                    logger.error(f"Failed to create directory structure for {pdf_name}")
                    return []

                # Use the PDF-specific tables directory
                table_dir = dir_structure["pdf_tables_dir"]
            else:
                # Only use temp_tables as a fallback if no category is provided
                # This should be rare in production use
                table_dir = os.path.join(TEMP_FOLDER, "temp_tables")
                os.makedirs(table_dir, exist_ok=True)
                logger.warning(f"No category provided for PDF {pdf_name}, using temporary directory for tables")

        # Extract tables from the PDF
        # Use lattice mode for tables with lines/borders
        lattice_tables = camelot.read_pdf(pdf_path, pages='all', flavor='lattice')
        # Use stream mode for tables without clear borders
        stream_tables = camelot.read_pdf(pdf_path, pages='all', flavor='stream')

        table_count = 0

        # Process lattice tables
        for i, table in enumerate(lattice_tables):
            if table.df.empty:
                continue

            table_count += 1
            # Convert table to HTML with better styling
            table_html = table.df.to_html(classes='table table-sm table-bordered table-responsive')

            # Enhance the HTML with better styling
            table_html = table_html.replace('<table', '<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;"')
            table_html = table_html.replace('<th', '<th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"')
            table_html = table_html.replace('<td', '<td style="border: 1px solid #dee2e6; padding: 0.3rem;"')

            if save_tables:
                table_filename = f"{pdf_base_name}_lattice_{i+1}.html"
                table_path = os.path.join(table_dir, table_filename)

                with open(table_path, "w", encoding="utf-8") as f:
                    f.write(table_html)

                table_info = {
                    "index": table_count,
                    "page": table.page,
                    "rows": table.shape[0],
                    "columns": table.shape[1],
                    "accuracy": table.accuracy,
                    "html": table_html,
                    "extraction_method": "camelot-lattice"
                }

                # Add URL for accessing the table through the Flask app
                # The URL format needs to be updated to match the new directory structure
                if category:
                    table_info["url"] = f"/{category}/{pdf_base_name}/pdf_tables/{table_filename}"
                else:
                    table_info["url"] = f"/temp_tables/{table_filename}"

                tables.append(table_info)

        # Process stream tables
        for i, table in enumerate(stream_tables):
            if table.df.empty:
                continue

            table_count += 1
            # Convert table to HTML with better styling
            table_html = table.df.to_html(classes='table table-sm table-bordered table-responsive')

            # Enhance the HTML with better styling
            table_html = table_html.replace('<table', '<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;"')
            table_html = table_html.replace('<th', '<th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"')
            table_html = table_html.replace('<td', '<td style="border: 1px solid #dee2e6; padding: 0.3rem;"')

            if save_tables:
                table_filename = f"{pdf_base_name}_stream_{i+1}.html"
                table_path = os.path.join(table_dir, table_filename)

                with open(table_path, "w", encoding="utf-8") as f:
                    f.write(table_html)

                table_info = {
                    "index": table_count,
                    "page": table.page,
                    "rows": table.shape[0],
                    "columns": table.shape[1],
                    "accuracy": table.accuracy,
                    "html": table_html,
                    "extraction_method": "camelot-stream"
                }

                # Add URL for accessing the table through the Flask app
                # The URL format needs to be updated to match the new directory structure
                if category:
                    table_info["url"] = f"/{category}/{pdf_base_name}/pdf_tables/{table_filename}"
                else:
                    table_info["url"] = f"/temp_tables/{table_filename}"

                tables.append(table_info)

        return tables
    except Exception as e:
        logger.error(f"Failed to extract tables with camelot from PDF {pdf_path}: {str(e)}")
        return []

def process_pdf(pdf_path, category=None, source_url=None, extract_tables=True, save_images=True, save_tables=True,
              use_vision=None, filter_sensitivity=None, max_images=None, extract_locations=True):
    """
    Process a PDF file to extract text, images, tables, links, and geographical locations.

    This function is the central point for PDF processing and should be called only once per PDF.
    It extracts all content and saves resources in the hierarchical directory structure.

    Args:
        pdf_path: Path to the PDF file
        category: Category for organizing extracted content
        source_url: Original URL where the PDF was obtained
        extract_tables: Whether to extract tables
        save_images: Whether to save extracted images to disk (set to False to only extract metadata)
        save_tables: Whether to save extracted tables to disk
        use_vision: Whether to use vision model for image analysis
        filter_sensitivity: Sensitivity level for filtering images (low, medium, high)
        max_images: Maximum number of images to save

    Returns:
        Dictionary containing extracted content and metadata
    """
    pdf_name = os.path.basename(pdf_path)

    # Always create the directory structure if category is provided
    # This ensures all resources are stored in the hierarchical structure
    if category:
        dir_structure = create_pdf_directory_structure(category, pdf_name)
        if dir_structure:
            # If we're moving an existing PDF to the new structure, copy it to the new location
            if os.path.exists(pdf_path) and not pdf_path.startswith(dir_structure["pdf_dir"]):
                new_pdf_path = dir_structure["pdf_path"]
                try:
                    shutil.copy2(pdf_path, new_pdf_path)
                    logger.info(f"Copied PDF from {pdf_path} to {new_pdf_path}")
                    # Update the pdf_path to the new location
                    pdf_path = new_pdf_path
                except Exception as e:
                    logger.error(f"Failed to copy PDF to new location: {str(e)}")
        else:
            logger.error(f"Failed to create directory structure for {pdf_name}")
    else:
        logger.warning(f"No category provided for PDF {pdf_name}. Resources will be stored in temporary directories.")

    result = {
        "text": [],
        "images": [],
        "tables": [],
        "links": [],
        "metadata": {
            "filename": pdf_name,
            "category": category,
            "source_url": source_url,
            "extraction_date": datetime.now().isoformat()
        }
    }

    # Extract cover image for thumbnail
    if category and save_images:
        thumbnail_info = extract_cover_image_from_pdf(pdf_path, category)
        if thumbnail_info:
            result["metadata"]["thumbnail_path"] = thumbnail_info["path"]
            result["metadata"]["thumbnail_url"] = thumbnail_info["url"]
            result["metadata"]["thumbnail_source"] = thumbnail_info["source"]
            result["metadata"]["thumbnail_description"] = thumbnail_info["description"]
            logger.info(f"Added thumbnail from PDF first page for {pdf_name}")
        else:
            # If we couldn't extract a cover image but have a source URL, try to fetch an image now
            if source_url:
                try:
                    # Try to fetch an image from the source URL
                    logger.info(f"Attempting to fetch thumbnail from source URL for {pdf_name}: {source_url}")

                    # Import necessary modules
                    import requests
                    from bs4 import BeautifulSoup
                    from urllib.parse import urljoin

                    # Fetch the webpage content
                    response = requests.get(source_url, timeout=10)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')

                        # Look for images in the page
                        images = []
                        for img in soup.find_all('img', src=True):
                            img_url = img['src']
                            if not img_url.startswith(('http://', 'https://')):
                                img_url = urljoin(source_url, img_url)

                            # Filter out small images and logos
                            if 'logo' not in img_url.lower() and not img_url.endswith(('.ico', '.svg')):
                                images.append(img_url)

                        # If we found images, use the first one
                        if images:
                            # Create directory structure for the cover image
                            cover_image_dir = os.path.join(dir_structure["pdf_images_dir"], "cover_image")
                            os.makedirs(cover_image_dir, exist_ok=True)

                            # Generate thumbnail filename
                            thumbnail_filename = f"{pdf_base_name}_url_thumbnail.jpg"
                            thumbnail_path = os.path.join(cover_image_dir, thumbnail_filename)

                            # Download and save the image
                            img_response = requests.get(images[0], timeout=10)
                            if img_response.status_code == 200:
                                with open(thumbnail_path, 'wb') as f:
                                    f.write(img_response.content)

                                # Create thumbnail info
                                result["metadata"]["thumbnail_path"] = thumbnail_path
                                result["metadata"]["thumbnail_url"] = f"/{category}/{pdf_base_name}/pdf_images/cover_image/{thumbnail_filename}"
                                result["metadata"]["thumbnail_source"] = "source_url"
                                result["metadata"]["thumbnail_description"] = f"Image from source URL for {pdf_name}"

                                logger.info(f"Successfully fetched thumbnail from source URL for {pdf_name}")
                            else:
                                # Failed to download the image
                                result["metadata"]["thumbnail_source"] = "default"
                                logger.warning(f"Failed to download image from {images[0]} for {pdf_name}")
                        else:
                            # No suitable images found
                            result["metadata"]["thumbnail_source"] = "default"
                            logger.warning(f"No suitable images found at source URL for {pdf_name}")
                    else:
                        # Failed to fetch the webpage
                        result["metadata"]["thumbnail_source"] = "default"
                        logger.warning(f"Failed to fetch source URL for {pdf_name}: {response.status_code}")
                except Exception as e:
                    # Error fetching from source URL, use default
                    result["metadata"]["thumbnail_source"] = "default"
                    logger.error(f"Error fetching thumbnail from source URL for {pdf_name}: {str(e)}")
            else:
                # No source URL, will use default category image
                result["metadata"]["thumbnail_source"] = "default"
                logger.info(f"Using default category thumbnail for {pdf_name}")
    elif not category:
        logger.warning(f"No category provided for PDF {pdf_name}, skipping thumbnail extraction")

    try:
        # Check if the PDF file exists
        if not os.path.exists(pdf_path):
            logger.error(f"PDF file not found: {pdf_path}")
            return result

        # Extract text using standard method
        standard_text = extract_text_standard(pdf_path)
        result["text"].extend(standard_text)

        # If standard extraction found little text, try OCR if available
        total_text = sum(len(page["text"]) for page in standard_text)
        if total_text < 1000 and HAS_OCR and HAS_OPENCV:  # Arbitrary threshold
            logger.info(f"Standard text extraction found limited text ({total_text} chars). Trying OCR...")
            ocr_text = extract_text_with_ocr(pdf_path)
            result["text"].extend(ocr_text)
        elif total_text < 1000:
            logger.warning("Limited text found but OCR dependencies not available. Text extraction may be incomplete.")

        # Extract images with vision model analysis if enabled
        images = extract_images_from_pdf(pdf_path, category, save_images, use_vision, filter_sensitivity, max_images)
        result["images"] = images

        # Add vision analysis metadata
        if images and "total_images_found" in images[0]:
            result["metadata"]["total_images_found"] = images[0]["total_images_found"]
            result["metadata"]["images_saved"] = images[0]["images_saved"]
            result["metadata"]["images_filtered"] = images[0].get("images_filtered", 0)
            result["metadata"]["vision_enabled"] = use_vision if use_vision is not None else os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'true').lower() == 'true'
            result["metadata"]["filter_sensitivity"] = filter_sensitivity if filter_sensitivity else os.getenv('PDF_IMAGE_FILTER_SENSITIVITY', 'medium')

        # Extract links
        links = extract_links_from_pdf(pdf_path)
        result["links"] = links

        # Extract tables if requested and dependencies are available
        if extract_tables:
            tables = []
            # Try camelot first (more accurate) if available
            if HAS_CAMELOT:
                tables = extract_tables_with_camelot(pdf_path, category, save_tables)

            # Fall back to tabula if camelot didn't find anything or isn't available
            if not tables and HAS_TABULA:
                tables = extract_tables_with_tabula(pdf_path, category, save_tables)

            if not tables and extract_tables:
                logger.warning("No tables found or table extraction libraries not available.")

            result["tables"] = tables

        # Add metadata about extraction results
        result["metadata"]["page_count"] = len(result["text"])
        result["metadata"]["image_count"] = len(result["images"])
        result["metadata"]["table_count"] = len(result["tables"])
        result["metadata"]["link_count"] = len(result["links"])

        # Add the new directory structure to metadata
        if category:
            pdf_base_name = os.path.splitext(pdf_name)[0]
            result["metadata"]["pdf_dir"] = f"/{category}/{pdf_base_name}"

        # Extract geographical locations if enabled
        locations_extracted = 0
        if extract_locations:
            try:
                from location_extractor import LocationExtractor
                from db_utils import save_extracted_location, save_location_source
                from db_content_utils import get_pdf_document_id

                logger.info(f"Extracting geographical locations from PDF {pdf_path}")
                location_extractor = LocationExtractor()

                # Extract locations from all text pages
                for page_data in result["text"]:
                    page_text = page_data.get("text", "")
                    page_number = page_data.get("page", 1)

                    if page_text.strip():
                        # Extract locations from this page
                        locations = location_extractor.extract_locations_from_text(page_text)

                        for location in locations:
                            # Try to geocode the location
                            from location_extractor import geocode_location
                            geocoding_result = geocode_location(location['location_text'])

                            if geocoding_result and geocoding_result.get('status') == 'success':
                                location.update({
                                    'latitude': geocoding_result['latitude'],
                                    'longitude': geocoding_result['longitude'],
                                    'geocoded_address': geocoding_result['formatted_address'],
                                    'country': geocoding_result['country'],
                                    'region': geocoding_result['region'],
                                    'city': geocoding_result['city'],
                                    'municipality': geocoding_result.get('municipality'),
                                    'barangay': geocoding_result.get('barangay')
                                })

                            # Save the location to database
                            location_id = save_extracted_location(location)

                            if location_id:
                                # Get PDF document ID for linking
                                pdf_filename = os.path.basename(pdf_path)
                                pdf_doc_id = get_pdf_document_id(pdf_filename, category)

                                if pdf_doc_id:
                                    save_location_source(
                                        location_id=location_id,
                                        source_type='pdf_document',
                                        source_id=pdf_doc_id,
                                        page_number=page_number,
                                        extraction_method=location['extraction_method']
                                    )
                                    locations_extracted += 1

                logger.info(f"Extracted and saved {locations_extracted} geographical locations from PDF")

            except Exception as e:
                logger.error(f"Error extracting locations from PDF {pdf_path}: {str(e)}")

        # Add location extraction metadata to the result
        result["metadata"]["locations_extracted"] = locations_extracted

        logger.info(f"Successfully processed PDF {pdf_path}: {result['metadata']['page_count']} pages, "
                   f"{result['metadata']['image_count']} images, {result['metadata']['table_count']} tables, "
                   f"{result['metadata']['link_count']} links, {locations_extracted} locations")

        return result
    except Exception as e:
        logger.error(f"Failed to process PDF {pdf_path}: {str(e)}")
        result["metadata"]["error"] = str(e)
        return result

def pdf_to_documents(pdf_path, category=None, source_url=None, use_vision=None, filter_sensitivity=None, max_images=None):
    """
    Convert a processed PDF to LangChain Document objects for vector storage.

    Args:
        pdf_path: Path to the PDF file
        category: Category for organizing content
        source_url: Original URL where the PDF was obtained
        use_vision: Whether to use vision model for image analysis
        filter_sensitivity: Sensitivity level for filtering images (low, medium, high)
        max_images: Maximum number of images to save

    Returns:
        List of Document objects ready for vector storage
    """
    # Process the PDF with vision model analysis if enabled
    processed_pdf = process_pdf(pdf_path, category, source_url,
                               use_vision=use_vision,
                               filter_sensitivity=filter_sensitivity,
                               max_images=max_images)

    if not processed_pdf["text"]:
        logger.warning(f"No text extracted from PDF {pdf_path}")
        return []

    # Create documents
    documents = []
    pdf_filename = os.path.basename(pdf_path)

    for page in processed_pdf["text"]:
        page_num = page["page"]
        page_text = page["text"]

        # Create metadata
        metadata = {
            "source": pdf_filename,
            "original_filename": pdf_filename,
            "citation_filename": os.path.basename(pdf_filename).split("_", 1)[1] if "_" in os.path.basename(pdf_filename) else pdf_filename,  # Remove timestamp prefix for citations
            "page": page_num,
            "type": "pdf",
            "extraction_method": page.get("extraction_method", "standard")
        }

        # Add source URL if provided
        if source_url:
            metadata["original_url"] = source_url

        # Add category if provided
        if category:
            metadata["category"] = category

        # Add images for this page
        page_images = [img for img in processed_pdf["images"] if img.get("page") == page_num]
        if page_images:
            metadata["images"] = json.dumps(page_images)
            metadata["image_count"] = len(page_images)

        # Add tables for this page
        page_tables = [table for table in processed_pdf["tables"] if table.get("page") == page_num]
        if page_tables:
            metadata["tables"] = json.dumps(page_tables)
            metadata["table_count"] = len(page_tables)

        # Add links
        if processed_pdf["links"]:
            metadata["pdf_links"] = json.dumps(processed_pdf["links"])
            metadata["link_count"] = len(processed_pdf["links"])

        # Create document
        documents.append(Document(page_content=page_text, metadata=metadata))

    # Split into chunks for better retrieval
    splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=250)
    chunks = splitter.split_documents(documents)

    # Ensure all chunks have the source filename and URL if provided
    for doc in chunks:
        doc.metadata["source"] = pdf_filename
        doc.metadata["original_filename"] = pdf_filename
        doc.metadata["citation_filename"] = os.path.basename(pdf_filename).split("_", 1)[1] if "_" in os.path.basename(pdf_filename) else pdf_filename  # Remove timestamp prefix for citations
        doc.metadata["type"] = "pdf"
        if source_url:
            doc.metadata["original_url"] = source_url
        if category:
            doc.metadata["category"] = category

        # Add thumbnail metadata if available in processed_pdf
        if "thumbnail_path" in processed_pdf["metadata"]:
            doc.metadata["thumbnail_path"] = processed_pdf["metadata"]["thumbnail_path"]
        if "thumbnail_url" in processed_pdf["metadata"]:
            doc.metadata["thumbnail_url"] = processed_pdf["metadata"]["thumbnail_url"]
        if "thumbnail_source" in processed_pdf["metadata"]:
            doc.metadata["thumbnail_source"] = processed_pdf["metadata"]["thumbnail_source"]
        if "thumbnail_description" in processed_pdf["metadata"]:
            doc.metadata["thumbnail_description"] = processed_pdf["metadata"]["thumbnail_description"]

    logger.info(f"Created {len(chunks)} document chunks from PDF {pdf_path}")
    return chunks
