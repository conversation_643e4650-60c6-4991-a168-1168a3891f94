/**
 * Lazy Loading and Image Optimization JavaScript
 * 
 * This module provides:
 * - Intersection Observer-based lazy loading
 * - Progressive image loading with placeholders
 * - WebP format detection and fallback
 * - Responsive image loading
 * - Performance monitoring
 * 
 * Version: 1.0.0
 */

class LazyImageLoader {
    constructor(options = {}) {
        this.options = {
            rootMargin: '50px 0px',
            threshold: 0.01,
            enableWebP: true,
            enableResponsive: true,
            fadeInDuration: 300,
            retryAttempts: 3,
            ...options
        };
        
        this.observer = null;
        this.webpSupported = null;
        this.stats = {
            imagesLoaded: 0,
            totalLoadTime: 0,
            bytesLoaded: 0,
            webpUsage: 0
        };
        
        this.init();
    }
    
    init() {
        // Check WebP support
        this.checkWebPSupport().then(supported => {
            this.webpSupported = supported;
            console.log(`WebP support: ${supported ? 'enabled' : 'disabled'}`);
        });
        
        // Initialize Intersection Observer
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver(
                this.handleIntersection.bind(this),
                {
                    rootMargin: this.options.rootMargin,
                    threshold: this.options.threshold
                }
            );
            
            // Start observing existing images
            this.observeImages();
        } else {
            // Fallback for older browsers
            this.loadAllImages();
        }
        
        // Add CSS for smooth transitions
        this.addLazyLoadingCSS();
    }
    
    checkWebPSupport() {
        return new Promise(resolve => {
            const webP = new Image();
            webP.onload = webP.onerror = () => {
                resolve(webP.height === 2);
            };
            webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
        });
    }
    
    observeImages() {
        const images = document.querySelectorAll('img[data-src], img[data-srcset]');
        images.forEach(img => {
            this.observer.observe(img);
        });
    }
    
    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                this.loadImage(entry.target);
                this.observer.unobserve(entry.target);
            }
        });
    }
    
    async loadImage(img) {
        const startTime = performance.now();
        
        try {
            // Get image sources
            const src = this.getOptimalImageSrc(img);
            const srcset = this.getOptimalImageSrcset(img);
            
            // Create new image for preloading
            const imageLoader = new Image();
            
            // Set up promise for loading
            const loadPromise = new Promise((resolve, reject) => {
                imageLoader.onload = () => resolve(imageLoader);
                imageLoader.onerror = () => reject(new Error('Image load failed'));
            });
            
            // Start loading
            if (srcset) {
                imageLoader.srcset = srcset;
            }
            imageLoader.src = src;
            
            // Wait for image to load
            await loadPromise;
            
            // Apply loaded image to actual img element
            this.applyLoadedImage(img, imageLoader, startTime);
            
        } catch (error) {
            console.error('Error loading image:', error);
            this.handleImageError(img);
        }
    }
    
    getOptimalImageSrc(img) {
        const dataSrc = img.dataset.src;
        
        if (!dataSrc) return img.src;
        
        // Check for WebP version if supported
        if (this.webpSupported && this.options.enableWebP) {
            const webpSrc = img.dataset.srcWebp;
            if (webpSrc) {
                this.stats.webpUsage++;
                return webpSrc;
            }
        }
        
        return dataSrc;
    }
    
    getOptimalImageSrcset(img) {
        const dataSrcset = img.dataset.srcset;
        
        if (!dataSrcset || !this.options.enableResponsive) {
            return null;
        }
        
        // Check for WebP srcset if supported
        if (this.webpSupported && this.options.enableWebP) {
            const webpSrcset = img.dataset.srcsetWebp;
            if (webpSrcset) {
                return webpSrcset;
            }
        }
        
        return dataSrcset;
    }
    
    applyLoadedImage(img, loadedImage, startTime) {
        // Calculate load time
        const loadTime = performance.now() - startTime;
        
        // Update statistics
        this.stats.imagesLoaded++;
        this.stats.totalLoadTime += loadTime;
        
        // Apply the loaded image
        if (loadedImage.srcset) {
            img.srcset = loadedImage.srcset;
        }
        img.src = loadedImage.src;
        
        // Remove placeholder class and add loaded class
        img.classList.remove('lazy-loading');
        img.classList.add('lazy-loaded');
        
        // Trigger fade-in animation
        this.fadeInImage(img);
        
        // Clean up data attributes
        delete img.dataset.src;
        delete img.dataset.srcset;
        delete img.dataset.srcWebp;
        delete img.dataset.srcsetWebp;
        
        console.log(`Image loaded in ${loadTime.toFixed(2)}ms:`, img.src);
    }
    
    fadeInImage(img) {
        if (this.options.fadeInDuration > 0) {
            img.style.transition = `opacity ${this.options.fadeInDuration}ms ease-in-out`;
            img.style.opacity = '1';
        }
    }
    
    handleImageError(img) {
        img.classList.remove('lazy-loading');
        img.classList.add('lazy-error');
        
        // Try fallback image if available
        const fallbackSrc = img.dataset.fallback;
        if (fallbackSrc && img.src !== fallbackSrc) {
            img.src = fallbackSrc;
        }
    }
    
    loadAllImages() {
        // Fallback for browsers without Intersection Observer
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => this.loadImage(img));
    }
    
    addLazyLoadingCSS() {
        const css = `
            .lazy-loading {
                opacity: 0.3;
                transition: opacity 300ms ease-in-out;
                background-color: #f0f0f0;
                background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                                  linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                                  linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                                  linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
                background-size: 20px 20px;
                background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            }
            
            .lazy-loaded {
                opacity: 1;
            }
            
            .lazy-error {
                opacity: 0.5;
                background-color: #ffebee;
                border: 1px solid #ffcdd2;
            }
            
            .lazy-placeholder {
                filter: blur(5px);
                transform: scale(1.05);
                transition: filter 300ms ease-in-out, transform 300ms ease-in-out;
            }
            
            .lazy-placeholder.loaded {
                filter: blur(0);
                transform: scale(1);
            }
        `;
        
        const style = document.createElement('style');
        style.textContent = css;
        document.head.appendChild(style);
    }
    
    // Public methods
    
    addImage(img) {
        if (this.observer) {
            this.observer.observe(img);
        } else {
            this.loadImage(img);
        }
    }
    
    refresh() {
        this.observeImages();
    }
    
    getStats() {
        const avgLoadTime = this.stats.imagesLoaded > 0 
            ? this.stats.totalLoadTime / this.stats.imagesLoaded 
            : 0;
            
        return {
            imagesLoaded: this.stats.imagesLoaded,
            averageLoadTime: Math.round(avgLoadTime),
            totalLoadTime: Math.round(this.stats.totalLoadTime),
            webpUsage: this.stats.webpUsage,
            webpUsagePercentage: this.stats.imagesLoaded > 0 
                ? Math.round((this.stats.webpUsage / this.stats.imagesLoaded) * 100)
                : 0
        };
    }
    
    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }
    }
}

// Utility functions for creating lazy-loaded images

function createLazyImage(src, options = {}) {
    const img = document.createElement('img');
    
    // Set placeholder if provided
    if (options.placeholder) {
        img.src = `data:image/jpeg;base64,${options.placeholder}`;
        img.classList.add('lazy-placeholder');
    }
    
    // Set data attributes for lazy loading
    img.dataset.src = src;
    
    if (options.srcset) {
        img.dataset.srcset = options.srcset;
    }
    
    if (options.webpSrc) {
        img.dataset.srcWebp = options.webpSrc;
    }
    
    if (options.webpSrcset) {
        img.dataset.srcsetWebp = options.webpSrcset;
    }
    
    if (options.fallback) {
        img.dataset.fallback = options.fallback;
    }
    
    // Set other attributes
    if (options.alt) img.alt = options.alt;
    if (options.className) img.className = options.className;
    if (options.loading) img.loading = options.loading;
    
    // Add lazy loading class
    img.classList.add('lazy-loading');
    
    return img;
}

function updateImageWithOptimization(img, optimizationResult) {
    if (!optimizationResult) return;
    
    const { variants, placeholder, optimized } = optimizationResult;
    
    // Set placeholder
    if (placeholder) {
        img.src = `data:image/jpeg;base64,${placeholder}`;
        img.classList.add('lazy-placeholder');
    }
    
    // Set main source
    if (optimized && optimized.format_used === 'webp') {
        img.dataset.srcWebp = optimized.path;
    } else {
        img.dataset.src = optimized ? optimized.path : img.src;
    }
    
    // Set responsive variants
    if (variants && Object.keys(variants).length > 0) {
        const srcsetEntries = Object.entries(variants).map(([size, path]) => `${path} ${size}`);
        img.dataset.srcset = srcsetEntries.join(', ');
    }
    
    img.classList.add('lazy-loading');
}

// Initialize lazy loading when DOM is ready
let lazyLoader = null;

function initializeLazyLoading(options = {}) {
    if (lazyLoader) {
        lazyLoader.destroy();
    }
    
    lazyLoader = new LazyImageLoader(options);
    return lazyLoader;
}

// Auto-initialize if not in module environment
if (typeof module === 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        initializeLazyLoading();
    });
}

// Export for module environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        LazyImageLoader,
        createLazyImage,
        updateImageWithOptimization,
        initializeLazyLoading
    };
}
