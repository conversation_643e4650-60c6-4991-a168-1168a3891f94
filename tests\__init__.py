"""
Test Suite for Document Management System

This package provides comprehensive test coverage for the application,
including unit tests, integration tests, and performance tests.

Features:
- Unit tests for all services and repositories
- Integration tests for end-to-end workflows
- Performance tests for optimization validation
- Mock objects and test fixtures
- Test data management
- Coverage reporting

Version: 1.0.0
"""

import os
import sys
import logging
import tempfile
import shutil
from typing import Any, Dict, List, Optional
from unittest.mock import Mock, MagicMock
from pathlib import Path

# Add the parent directory to the path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure test logging
logging.basicConfig(
    level=logging.WARNING,  # Reduce noise during tests
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class TestConfig:
    """Configuration for test environment"""
    
    def __init__(self):
        self.test_dir = tempfile.mkdtemp(prefix='dms_test_')
        self.test_db_path = os.path.join(self.test_dir, 'test_knowledge_base.db')
        self.test_uploads_dir = os.path.join(self.test_dir, 'uploads')
        self.test_temp_dir = os.path.join(self.test_dir, '_temp')
        
        # Create test directories
        os.makedirs(self.test_uploads_dir, exist_ok=True)
        os.makedirs(self.test_temp_dir, exist_ok=True)
        
        # Set environment variables for testing
        os.environ['DATABASE_PATH'] = self.test_db_path
        os.environ['UPLOADS_FOLDER'] = self.test_uploads_dir
        os.environ['TEMP_FOLDER'] = self.test_temp_dir
        os.environ['CACHE_BACKEND'] = 'memory'
        os.environ['TESTING'] = 'true'
    
    def cleanup(self):
        """Clean up test environment"""
        try:
            shutil.rmtree(self.test_dir)
        except Exception as e:
            logging.warning(f"Failed to cleanup test directory: {e}")

class MockServices:
    """Mock objects for testing"""
    
    @staticmethod
    def create_mock_cache_service():
        """Create mock cache service"""
        mock_cache = Mock()
        mock_cache.get.return_value = None
        mock_cache.set.return_value = True
        mock_cache.delete.return_value = True
        mock_cache.clear.return_value = True
        mock_cache.get_stats.return_value = Mock(hits=0, misses=0, sets=0, deletes=0)
        return mock_cache
    
    @staticmethod
    def create_mock_database_pool():
        """Create mock database connection pool"""
        mock_pool = Mock()
        mock_connection = Mock()
        mock_cursor = Mock()
        
        mock_cursor.fetchone.return_value = None
        mock_cursor.fetchall.return_value = []
        mock_cursor.lastrowid = 1
        mock_cursor.rowcount = 1
        
        mock_connection.execute.return_value = mock_cursor
        mock_connection.commit.return_value = None
        mock_connection.rollback.return_value = None
        
        mock_pool.get_connection.return_value.__enter__.return_value = mock_connection
        mock_pool.get_connection.return_value.__exit__.return_value = None
        
        return mock_pool
    
    @staticmethod
    def create_mock_file_optimizer():
        """Create mock file I/O optimizer"""
        mock_optimizer = Mock()
        mock_optimizer.get_file_size_mb.return_value = 1.0
        mock_optimizer.optimize_image_streaming.return_value = '/tmp/optimized.jpg'
        mock_optimizer.get_processing_stats.return_value = {
            'files_processed': 0,
            'memory_saved_mb': 0.0,
            'avg_speed_mb_per_second': 0.0
        }
        return mock_optimizer
    
    @staticmethod
    def create_mock_image_optimizer():
        """Create mock image optimizer"""
        mock_optimizer = Mock()
        mock_optimizer.optimize_image.return_value = Mock(
            original_size=1024000,
            optimized_size=512000,
            compression_ratio=0.5,
            format_used='jpeg',
            processing_time=0.1
        )
        mock_optimizer.get_optimization_stats.return_value = {
            'images_processed': 0,
            'total_size_saved_mb': 0.0,
            'webp_conversions': 0
        }
        return mock_optimizer

class TestDataFactory:
    """Factory for creating test data"""
    
    @staticmethod
    def create_test_document(filename: str = "test.pdf", category: str = "test") -> Dict[str, Any]:
        """Create test document data"""
        return {
            'id': '1',
            'filename': filename,
            'category': category,
            'upload_date': '2024-01-01T00:00:00',
            'file_size': 1024000,
            'source_url': 'https://example.com/test.pdf'
        }
    
    @staticmethod
    def create_test_analytics_record(query: str = "test query") -> Dict[str, Any]:
        """Create test analytics record"""
        return {
            'id': '1',
            'query': query,
            'category': 'test',
            'timestamp': '2024-01-01T00:00:00',
            'processing_time': 1.5,
            'model_used': 'test-model',
            'client_name': 'test-client',
            'device_fingerprint': 'test-fingerprint'
        }
    
    @staticmethod
    def create_test_user(client_name: str = "test-user") -> Dict[str, Any]:
        """Create test user data"""
        return {
            'id': '1',
            'client_name': client_name,
            'device_fingerprint': 'test-fingerprint',
            'first_visit': '2024-01-01T00:00:00',
            'last_activity': '2024-01-01T00:00:00',
            'session_count': 1
        }
    
    @staticmethod
    def create_test_pdf_file(content: str = "Test PDF content") -> str:
        """Create a test PDF file"""
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter
            
            # Create temporary PDF file
            fd, path = tempfile.mkstemp(suffix='.pdf')
            
            with os.fdopen(fd, 'wb') as tmp_file:
                # Create PDF with reportlab
                c = canvas.Canvas(tmp_file, pagesize=letter)
                c.drawString(100, 750, content)
                c.save()
            
            return path
            
        except ImportError:
            # Fallback: create a fake PDF file
            fd, path = tempfile.mkstemp(suffix='.pdf')
            with os.fdopen(fd, 'w') as tmp_file:
                tmp_file.write(f"%PDF-1.4\n1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n")
                tmp_file.write(f"2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n")
                tmp_file.write(f"3 0 obj\n<< /Type /Page /Parent 2 0 R /Contents 4 0 R >>\nendobj\n")
                tmp_file.write(f"4 0 obj\n<< /Length 44 >>\nstream\nBT\n/F1 12 Tf\n100 750 Td\n({content}) Tj\nET\nendstream\nendobj\n")
                tmp_file.write(f"xref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n")
                tmp_file.write(f"trailer\n<< /Size 5 /Root 1 0 R >>\nstartxref\n%%EOF")
            
            return path
    
    @staticmethod
    def create_test_image_file(width: int = 100, height: int = 100) -> str:
        """Create a test image file"""
        try:
            from PIL import Image
            
            # Create temporary image file
            fd, path = tempfile.mkstemp(suffix='.jpg')
            
            with os.fdopen(fd, 'wb'):
                pass  # Close the file descriptor
            
            # Create image with PIL
            img = Image.new('RGB', (width, height), color='red')
            img.save(path, 'JPEG')
            
            return path
            
        except ImportError:
            # Fallback: create empty file
            fd, path = tempfile.mkstemp(suffix='.jpg')
            with os.fdopen(fd, 'wb') as tmp_file:
                # Write minimal JPEG header
                tmp_file.write(b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00')
            
            return path

class BaseTestCase:
    """Base test case with common setup and teardown"""
    
    def setup_method(self):
        """Set up test environment"""
        self.test_config = TestConfig()
        self.mock_services = MockServices()
        self.test_data = TestDataFactory()
    
    def teardown_method(self):
        """Clean up test environment"""
        self.test_config.cleanup()
    
    def assert_dict_contains(self, actual: Dict[str, Any], expected: Dict[str, Any]):
        """Assert that actual dictionary contains all expected key-value pairs"""
        for key, value in expected.items():
            assert key in actual, f"Key '{key}' not found in actual dictionary"
            assert actual[key] == value, f"Expected {key}={value}, got {actual[key]}"
    
    def assert_list_contains_dict(self, actual_list: List[Dict[str, Any]], expected_dict: Dict[str, Any]):
        """Assert that list contains a dictionary with expected key-value pairs"""
        for item in actual_list:
            try:
                self.assert_dict_contains(item, expected_dict)
                return  # Found matching item
            except AssertionError:
                continue
        
        raise AssertionError(f"No item in list matches expected dictionary: {expected_dict}")

# Test configuration singleton
_test_config: Optional[TestConfig] = None

def get_test_config() -> TestConfig:
    """Get global test configuration"""
    global _test_config
    if _test_config is None:
        _test_config = TestConfig()
    return _test_config

def cleanup_test_config():
    """Clean up global test configuration"""
    global _test_config
    if _test_config:
        _test_config.cleanup()
        _test_config = None

# Export main classes and functions
__all__ = [
    'TestConfig',
    'MockServices', 
    'TestDataFactory',
    'BaseTestCase',
    'get_test_config',
    'cleanup_test_config'
]
