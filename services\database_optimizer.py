"""
Database Optimizer Service

This service provides enhanced database connection pooling, query optimization,
and performance monitoring for improved database operations.

Features:
- Connection pooling with configurable pool size
- Query performance monitoring and optimization
- Automatic connection health checks
- Database operation caching
- Transaction management
- Connection leak detection
- Performance analytics

Version: 1.0.0
"""

import sqlite3
import threading
import time
import logging
import queue
import os
from typing import Optional, Dict, Any, List, Callable, ContextManager
from dataclasses import dataclass, field
from contextlib import contextmanager
import weakref
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class ConnectionPoolConfig:
    """Configuration for database connection pool"""
    min_connections: int = 5
    max_connections: int = 20
    connection_timeout: float = 30.0
    idle_timeout: float = 300.0  # 5 minutes
    health_check_interval: float = 60.0  # 1 minute
    enable_wal_mode: bool = True
    enable_foreign_keys: bool = True
    cache_size: int = -64000  # 64MB cache
    busy_timeout: int = 30000  # 30 seconds
    
@dataclass
class QueryStats:
    """Statistics for database queries"""
    query_hash: str
    execution_count: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    avg_time: float = 0.0
    last_executed: datetime = field(default_factory=datetime.now)
    
    def update(self, execution_time: float):
        """Update statistics with new execution time"""
        self.execution_count += 1
        self.total_time += execution_time
        self.min_time = min(self.min_time, execution_time)
        self.max_time = max(self.max_time, execution_time)
        self.avg_time = self.total_time / self.execution_count
        self.last_executed = datetime.now()

class DatabaseConnection:
    """Wrapper for database connection with health tracking"""
    
    def __init__(self, db_path: str, config: ConnectionPoolConfig):
        self.db_path = db_path
        self.config = config
        self.connection = None
        self.created_at = time.time()
        self.last_used = time.time()
        self.is_healthy = True
        self.in_use = False
        self.transaction_active = False
        
        self._create_connection()
    
    def _create_connection(self):
        """Create and configure database connection"""
        try:
            self.connection = sqlite3.connect(
                self.db_path,
                timeout=self.config.busy_timeout / 1000.0,
                check_same_thread=False
            )
            
            # Configure connection
            self.connection.row_factory = sqlite3.Row
            
            # Enable WAL mode for better concurrency
            if self.config.enable_wal_mode:
                self.connection.execute("PRAGMA journal_mode=WAL")
            
            # Enable foreign keys
            if self.config.enable_foreign_keys:
                self.connection.execute("PRAGMA foreign_keys=ON")
            
            # Set cache size
            self.connection.execute(f"PRAGMA cache_size={self.config.cache_size}")
            
            # Set busy timeout
            self.connection.execute(f"PRAGMA busy_timeout={self.config.busy_timeout}")
            
            # Optimize for performance
            self.connection.execute("PRAGMA synchronous=NORMAL")
            self.connection.execute("PRAGMA temp_store=MEMORY")
            
            self.connection.commit()
            self.is_healthy = True
            
        except Exception as e:
            logger.error(f"Failed to create database connection: {e}")
            self.is_healthy = False
            raise
    
    def health_check(self) -> bool:
        """Check if connection is healthy"""
        try:
            if not self.connection:
                return False
            
            # Simple query to test connection
            cursor = self.connection.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            
            self.is_healthy = True
            return True
            
        except Exception as e:
            logger.warning(f"Connection health check failed: {e}")
            self.is_healthy = False
            return False
    
    def is_idle_timeout(self) -> bool:
        """Check if connection has exceeded idle timeout"""
        return (time.time() - self.last_used) > self.config.idle_timeout
    
    def mark_used(self):
        """Mark connection as recently used"""
        self.last_used = time.time()
        self.in_use = True
    
    def mark_unused(self):
        """Mark connection as not in use"""
        self.in_use = False
    
    def close(self):
        """Close the database connection"""
        if self.connection:
            try:
                if self.transaction_active:
                    self.connection.rollback()
                self.connection.close()
            except Exception as e:
                logger.warning(f"Error closing connection: {e}")
            finally:
                self.connection = None
                self.is_healthy = False

class DatabaseConnectionPool:
    """Enhanced database connection pool with monitoring"""
    
    def __init__(self, db_path: str, config: Optional[ConnectionPoolConfig] = None):
        self.db_path = db_path
        self.config = config or ConnectionPoolConfig()
        
        # Connection management
        self._pool = queue.Queue(maxsize=self.config.max_connections)
        self._all_connections = weakref.WeakSet()
        self._pool_lock = threading.RLock()
        self._active_connections = 0
        
        # Performance monitoring
        self.query_stats: Dict[str, QueryStats] = {}
        self.stats_lock = threading.Lock()
        
        # Pool statistics
        self.pool_stats = {
            'connections_created': 0,
            'connections_destroyed': 0,
            'connections_reused': 0,
            'pool_hits': 0,
            'pool_misses': 0,
            'health_checks_performed': 0,
            'health_check_failures': 0
        }
        
        # Initialize minimum connections
        self._initialize_pool()
        
        # Start health check thread
        self._health_check_thread = threading.Thread(
            target=self._health_check_worker,
            daemon=True
        )
        self._health_check_thread.start()
    
    def _initialize_pool(self):
        """Initialize the connection pool with minimum connections"""
        for _ in range(self.config.min_connections):
            try:
                conn = self._create_connection()
                self._pool.put(conn, block=False)
            except Exception as e:
                logger.error(f"Failed to initialize connection pool: {e}")
                break
    
    def _create_connection(self) -> DatabaseConnection:
        """Create a new database connection"""
        with self._pool_lock:
            if self._active_connections >= self.config.max_connections:
                raise Exception("Maximum number of connections reached")
            
            conn = DatabaseConnection(self.db_path, self.config)
            self._all_connections.add(conn)
            self._active_connections += 1
            self.pool_stats['connections_created'] += 1
            
            return conn
    
    def _health_check_worker(self):
        """Background worker for connection health checks"""
        while True:
            try:
                time.sleep(self.config.health_check_interval)
                self._perform_health_checks()
            except Exception as e:
                logger.error(f"Health check worker error: {e}")
    
    def _perform_health_checks(self):
        """Perform health checks on all connections"""
        unhealthy_connections = []
        
        # Check connections in pool
        temp_connections = []
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                self.pool_stats['health_checks_performed'] += 1
                
                if conn.health_check() and not conn.is_idle_timeout():
                    temp_connections.append(conn)
                else:
                    unhealthy_connections.append(conn)
                    self.pool_stats['health_check_failures'] += 1
                    
            except queue.Empty:
                break
        
        # Put healthy connections back
        for conn in temp_connections:
            try:
                self._pool.put_nowait(conn)
            except queue.Full:
                unhealthy_connections.append(conn)
        
        # Close unhealthy connections
        for conn in unhealthy_connections:
            self._close_connection(conn)
    
    def _close_connection(self, conn: DatabaseConnection):
        """Close and remove a connection from the pool"""
        with self._pool_lock:
            conn.close()
            self._active_connections -= 1
            self.pool_stats['connections_destroyed'] += 1
    
    @contextmanager
    def get_connection(self) -> ContextManager[sqlite3.Connection]:
        """Get a connection from the pool"""
        conn = None
        try:
            # Try to get connection from pool
            try:
                conn = self._pool.get(timeout=self.config.connection_timeout)
                self.pool_stats['pool_hits'] += 1
                self.pool_stats['connections_reused'] += 1
            except queue.Empty:
                # Create new connection if pool is empty
                conn = self._create_connection()
                self.pool_stats['pool_misses'] += 1
            
            # Mark connection as in use
            conn.mark_used()
            
            yield conn.connection
            
        finally:
            if conn:
                # Mark connection as not in use
                conn.mark_unused()
                
                # Return to pool if healthy
                if conn.is_healthy and not conn.is_idle_timeout():
                    try:
                        self._pool.put_nowait(conn)
                    except queue.Full:
                        self._close_connection(conn)
                else:
                    self._close_connection(conn)
    
    def execute_query(self, query: str, params: tuple = None, fetch_one: bool = False, fetch_all: bool = True) -> Any:
        """Execute a query with performance monitoring"""
        import hashlib
        
        # Generate query hash for statistics
        query_hash = hashlib.md5(query.encode()).hexdigest()[:8]
        
        start_time = time.time()
        
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(query, params or ())
                
                if fetch_one:
                    result = cursor.fetchone()
                elif fetch_all:
                    result = cursor.fetchall()
                else:
                    result = cursor
                
                conn.commit()
                
                # Update query statistics
                execution_time = time.time() - start_time
                self._update_query_stats(query_hash, execution_time)
                
                return result
                
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            raise
    
    def _update_query_stats(self, query_hash: str, execution_time: float):
        """Update query performance statistics"""
        with self.stats_lock:
            if query_hash not in self.query_stats:
                self.query_stats[query_hash] = QueryStats(query_hash)
            
            self.query_stats[query_hash].update(execution_time)
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """Get comprehensive pool statistics"""
        with self._pool_lock:
            return {
                'active_connections': self._active_connections,
                'pool_size': self._pool.qsize(),
                'max_connections': self.config.max_connections,
                'min_connections': self.config.min_connections,
                **self.pool_stats
            }
    
    def get_query_performance_stats(self) -> List[Dict[str, Any]]:
        """Get query performance statistics"""
        with self.stats_lock:
            stats = []
            for query_stat in self.query_stats.values():
                stats.append({
                    'query_hash': query_stat.query_hash,
                    'execution_count': query_stat.execution_count,
                    'avg_time_ms': round(query_stat.avg_time * 1000, 2),
                    'min_time_ms': round(query_stat.min_time * 1000, 2),
                    'max_time_ms': round(query_stat.max_time * 1000, 2),
                    'total_time_ms': round(query_stat.total_time * 1000, 2),
                    'last_executed': query_stat.last_executed.isoformat()
                })
            
            # Sort by total time (most expensive queries first)
            stats.sort(key=lambda x: x['total_time_ms'], reverse=True)
            return stats
    
    def close_all(self):
        """Close all connections in the pool"""
        with self._pool_lock:
            # Close connections in pool
            while not self._pool.empty():
                try:
                    conn = self._pool.get_nowait()
                    conn.close()
                except queue.Empty:
                    break
            
            # Close any remaining connections
            for conn in list(self._all_connections):
                conn.close()
            
            self._active_connections = 0

# Global connection pool instance
_connection_pool: Optional[DatabaseConnectionPool] = None

def get_database_pool() -> DatabaseConnectionPool:
    """Get global database connection pool instance"""
    global _connection_pool
    if _connection_pool is None:
        # Get database path
        db_path = os.getenv('DATABASE_PATH', 'knowledge_base.db')
        
        # Create configuration from environment
        config = ConnectionPoolConfig(
            min_connections=int(os.getenv('DB_MIN_CONNECTIONS', '5')),
            max_connections=int(os.getenv('DB_MAX_CONNECTIONS', '20')),
            connection_timeout=float(os.getenv('DB_CONNECTION_TIMEOUT', '30.0')),
            idle_timeout=float(os.getenv('DB_IDLE_TIMEOUT', '300.0')),
            enable_wal_mode=os.getenv('DB_ENABLE_WAL', 'true').lower() == 'true',
            cache_size=int(os.getenv('DB_CACHE_SIZE', '-64000'))
        )
        
        _connection_pool = DatabaseConnectionPool(db_path, config)
    
    return _connection_pool
