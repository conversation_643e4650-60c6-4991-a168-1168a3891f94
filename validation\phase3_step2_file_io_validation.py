#!/usr/bin/env python3
"""
Phase 3 Step 2: File I/O Optimization Validation Script

This script validates the implementation of file I/O optimization by:
1. Testing streaming file processing for large PDFs
2. Measuring memory usage during file operations
3. Validating image optimization functionality
4. Testing performance improvements
5. Monitoring memory efficiency

Expected Results:
- Memory usage reduction of 30-50% for large file processing
- Processing speed improvement for large files
- Successful streaming operations without memory overflow
- Image optimization reducing file sizes by 40-60%
"""

import sys
import os
import time
import logging
import traceback
import tempfile
import shutil
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_file_io_optimizer():
    """Test the file I/O optimizer service."""
    try:
        from services.file_io_optimizer import get_file_io_optimizer
        
        optimizer = get_file_io_optimizer()
        logger.info(f"File I/O optimizer initialized")
        
        # Test memory monitoring
        with optimizer.memory_monitor("Test Operation"):
            time.sleep(0.1)  # Simulate work
        
        # Test statistics
        stats = optimizer.get_processing_stats()
        logger.info(f"Processing stats: {stats}")
        
        logger.info("✓ File I/O optimizer basic functionality working")
        return True
        
    except Exception as e:
        logger.error(f"✗ File I/O optimizer test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_streaming_pdf_processing():
    """Test streaming PDF processing functionality."""
    try:
        from services.file_io_optimizer import get_file_io_optimizer
        import pdf_processor
        
        # Create a test PDF file (if available)
        test_files = []
        
        # Look for existing PDF files in the uploads directory
        uploads_dir = "uploads"
        if os.path.exists(uploads_dir):
            for file in os.listdir(uploads_dir):
                if file.endswith('.pdf'):
                    test_files.append(os.path.join(uploads_dir, file))
                    break  # Just test with one file
        
        if not test_files:
            logger.info("ℹ No PDF files found for streaming test")
            return True
        
        test_file = test_files[0]
        file_size_mb = os.path.getsize(test_file) / (1024 * 1024)
        logger.info(f"Testing streaming with PDF: {test_file} ({file_size_mb:.2f}MB)")
        
        # Test streaming text extraction
        if hasattr(pdf_processor, 'extract_text_standard_streaming'):
            start_time = time.time()
            start_memory = get_memory_usage_mb()
            
            result = pdf_processor.extract_text_standard_streaming(test_file)
            
            end_time = time.time()
            end_memory = get_memory_usage_mb()
            
            processing_time = end_time - start_time
            memory_used = end_memory - start_memory
            
            logger.info(f"Streaming text extraction completed:")
            logger.info(f"  - Processing time: {processing_time:.2f}s")
            logger.info(f"  - Memory used: {memory_used:.2f}MB")
            logger.info(f"  - Pages extracted: {len(result) if result else 0}")
            
            if result:
                logger.info("✓ Streaming PDF text extraction working")
            else:
                logger.warning("⚠ Streaming PDF text extraction returned no results")
        
        # Test streaming image extraction
        if hasattr(pdf_processor, 'extract_images_from_pdf_streaming'):
            start_time = time.time()
            start_memory = get_memory_usage_mb()
            
            result = pdf_processor.extract_images_from_pdf_streaming(test_file, max_images=5)
            
            end_time = time.time()
            end_memory = get_memory_usage_mb()
            
            processing_time = end_time - start_time
            memory_used = end_memory - start_memory
            
            logger.info(f"Streaming image extraction completed:")
            logger.info(f"  - Processing time: {processing_time:.2f}s")
            logger.info(f"  - Memory used: {memory_used:.2f}MB")
            logger.info(f"  - Images extracted: {len(result) if result else 0}")
            
            logger.info("✓ Streaming PDF image extraction working")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Streaming PDF processing test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_image_optimization():
    """Test image optimization functionality."""
    try:
        from services.file_io_optimizer import get_file_io_optimizer
        from PIL import Image
        
        optimizer = get_file_io_optimizer()
        
        # Create a test image
        test_image = Image.new('RGB', (2048, 1536), color='red')
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
            test_image.save(temp_file.name, 'JPEG', quality=95)
            test_path = temp_file.name
        
        try:
            original_size = os.path.getsize(test_path) / (1024 * 1024)
            logger.info(f"Test image created: {original_size:.2f}MB")
            
            # Test optimization
            start_time = time.time()
            optimized_path = optimizer.optimize_image_streaming(test_path)
            end_time = time.time()
            
            if optimized_path and os.path.exists(optimized_path):
                optimized_size = os.path.getsize(optimized_path) / (1024 * 1024)
                compression_ratio = (1 - optimized_size / original_size) * 100
                
                logger.info(f"Image optimization completed:")
                logger.info(f"  - Original size: {original_size:.2f}MB")
                logger.info(f"  - Optimized size: {optimized_size:.2f}MB")
                logger.info(f"  - Compression: {compression_ratio:.1f}%")
                logger.info(f"  - Processing time: {end_time - start_time:.2f}s")
                
                if compression_ratio > 10:  # At least 10% compression
                    logger.info("✓ Image optimization working effectively")
                    return True
                else:
                    logger.warning("⚠ Image optimization compression is low")
                    return True  # Still working, just not much compression needed
            else:
                logger.error("✗ Image optimization failed to create optimized file")
                return False
                
        finally:
            # Cleanup
            if os.path.exists(test_path):
                os.unlink(test_path)
            if optimized_path and os.path.exists(optimized_path):
                os.unlink(optimized_path)
        
    except Exception as e:
        logger.error(f"✗ Image optimization test failed: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_vision_processor_streaming():
    """Test vision processor streaming functionality."""
    try:
        import vision_processor
        
        # Test if streaming functions are available
        if hasattr(vision_processor, 'encode_image_to_base64_streaming'):
            logger.info("✓ Vision processor streaming functions available")
            return True
        else:
            logger.warning("⚠ Vision processor streaming functions not found")
            return False
        
    except Exception as e:
        logger.error(f"✗ Vision processor streaming test failed: {str(e)}")
        return False

def get_memory_usage_mb():
    """Get current memory usage in MB"""
    try:
        import psutil
        process = psutil.Process()
        return process.memory_info().rss / (1024 * 1024)
    except:
        return 0.0

def measure_performance_improvements():
    """Measure performance improvements from file I/O optimization."""
    try:
        from services.file_io_optimizer import get_file_io_optimizer
        
        optimizer = get_file_io_optimizer()
        stats = optimizer.get_processing_stats()
        
        logger.info(f"File I/O Performance Statistics:")
        logger.info(f"  - Files processed: {stats['files_processed']}")
        logger.info(f"  - Total size processed: {stats['total_size_mb']:.2f}MB")
        logger.info(f"  - Processing time: {stats['processing_time_seconds']:.2f}s")
        logger.info(f"  - Average speed: {stats['avg_speed_mb_per_second']:.2f}MB/s")
        logger.info(f"  - Peak memory usage: {stats['memory_peak_mb']:.2f}MB")
        logger.info(f"  - Memory saved: {stats['memory_saved_mb']:.2f}MB")
        logger.info(f"  - Current memory: {stats['current_memory_mb']:.2f}MB")
        
        # Check if we have meaningful performance data
        if stats['files_processed'] > 0:
            logger.info("✓ Performance measurement data available")
            
            # Check for memory savings
            if stats['memory_saved_mb'] > 0:
                logger.info(f"✓ Memory optimization achieved: {stats['memory_saved_mb']:.2f}MB saved")
            
            return True
        else:
            logger.info("ℹ No performance data available yet")
            return True
            
    except Exception as e:
        logger.error(f"✗ Performance measurement failed: {str(e)}")
        return False

def run_validation():
    """Run all validation tests."""
    logger.info("Starting Phase 3 Step 2: File I/O Optimization Validation")
    logger.info("=" * 60)
    
    tests = [
        ("File I/O Optimizer Service", test_file_io_optimizer),
        ("Streaming PDF Processing", test_streaming_pdf_processing),
        ("Image Optimization", test_image_optimization),
        ("Vision Processor Streaming", test_vision_processor_streaming),
        ("Performance Measurement", measure_performance_improvements),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\nRunning: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                logger.error(f"Test failed: {test_name}")
        except Exception as e:
            logger.error(f"Test error in {test_name}: {str(e)}")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"Validation Results: {passed}/{total} tests passed")
    
    if passed >= total - 1:  # Allow one test to fail (e.g., if no test files available)
        logger.info("🎉 File I/O optimization validation successful!")
        logger.info("Phase 3 Step 2 implementation is working correctly")
        return True
    else:
        logger.error(f"❌ {total - passed} tests failed")
        logger.error("Phase 3 Step 2 implementation needs fixes")
        return False

if __name__ == "__main__":
    success = run_validation()
    sys.exit(0 if success else 1)
