"""
Tests for Dependency Injection Container

This module tests the dependency injection system including:
- Service registration and resolution
- Lifetime management (singleton, transient, scoped)
- Circular dependency detection
- Factory registration
- Service configuration

Version: 1.0.0
"""

import pytest
import threading
import time
from unittest.mock import Mock

from tests import BaseTestCase
from services.dependency_injection import (
    DependencyInjectionContainer, ServiceLifetime, 
    CircularDependencyError, ServiceNotRegisteredError,
    get_container, configure_services
)

class TestService:
    """Test service for dependency injection testing"""
    def __init__(self):
        self.created_at = time.time()
        self.id = id(self)

class TestServiceWithDependency:
    """Test service with dependency for injection testing"""
    def __init__(self, dependency: TestService):
        self.dependency = dependency
        self.created_at = time.time()

class TestDependencyInjectionContainer(BaseTestCase):
    """Test cases for dependency injection container"""
    
    def setup_method(self):
        super().setup_method()
        self.container = DependencyInjectionContainer()
    
    def test_register_singleton(self):
        """Test singleton service registration"""
        # Register service as singleton
        self.container.register_singleton(TestService)
        
        # Resolve multiple times
        instance1 = self.container.resolve(TestService)
        instance2 = self.container.resolve(TestService)
        
        # Should be the same instance
        assert instance1 is instance2
        assert instance1.id == instance2.id
    
    def test_register_transient(self):
        """Test transient service registration"""
        # Register service as transient
        self.container.register_transient(TestService)
        
        # Resolve multiple times
        instance1 = self.container.resolve(TestService)
        instance2 = self.container.resolve(TestService)
        
        # Should be different instances
        assert instance1 is not instance2
        assert instance1.id != instance2.id
    
    def test_register_scoped(self):
        """Test scoped service registration"""
        # Register service as scoped
        self.container.register_scoped(TestService)
        
        # Resolve multiple times in same scope
        instance1 = self.container.resolve(TestService)
        instance2 = self.container.resolve(TestService)
        
        # Should be the same instance in same scope
        assert instance1 is instance2
        
        # Clear scope and resolve again
        self.container.clear_scoped()
        instance3 = self.container.resolve(TestService)
        
        # Should be different instance after scope clear
        assert instance1 is not instance3
    
    def test_register_factory(self):
        """Test factory registration"""
        call_count = 0
        
        def test_factory():
            nonlocal call_count
            call_count += 1
            return TestService()
        
        # Register factory
        self.container.register_factory(TestService, test_factory, ServiceLifetime.TRANSIENT)
        
        # Resolve service
        instance = self.container.resolve(TestService)
        
        assert isinstance(instance, TestService)
        assert call_count == 1
    
    def test_register_instance(self):
        """Test instance registration"""
        # Create instance
        test_instance = TestService()
        
        # Register instance
        self.container.register_instance(TestService, test_instance)
        
        # Resolve service
        resolved_instance = self.container.resolve(TestService)
        
        # Should be the same instance
        assert resolved_instance is test_instance
    
    def test_dependency_injection(self):
        """Test automatic dependency injection"""
        # Register dependencies
        self.container.register_singleton(TestService)
        self.container.register_transient(TestServiceWithDependency)
        
        # Resolve service with dependency
        instance = self.container.resolve(TestServiceWithDependency)
        
        assert isinstance(instance, TestServiceWithDependency)
        assert isinstance(instance.dependency, TestService)
    
    def test_circular_dependency_detection(self):
        """Test circular dependency detection"""
        class ServiceA:
            def __init__(self, service_b: 'ServiceB'):
                self.service_b = service_b
        
        class ServiceB:
            def __init__(self, service_a: ServiceA):
                self.service_a = service_a
        
        # Register services with circular dependency
        self.container.register_transient(ServiceA)
        self.container.register_transient(ServiceB)
        
        # Should raise circular dependency error
        with pytest.raises(CircularDependencyError):
            self.container.resolve(ServiceA)
    
    def test_service_not_registered_error(self):
        """Test error when resolving unregistered service"""
        with pytest.raises(ServiceNotRegisteredError):
            self.container.resolve(TestService)
    
    def test_thread_safety(self):
        """Test thread safety of container"""
        self.container.register_singleton(TestService)
        
        instances = []
        
        def resolve_service():
            instance = self.container.resolve(TestService)
            instances.append(instance)
        
        # Create multiple threads
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=resolve_service)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All instances should be the same (singleton)
        first_instance = instances[0]
        for instance in instances:
            assert instance is first_instance
    
    def test_is_registered(self):
        """Test service registration check"""
        # Initially not registered
        assert not self.container.is_registered(TestService)
        
        # Register service
        self.container.register_singleton(TestService)
        
        # Now should be registered
        assert self.container.is_registered(TestService)
    
    def test_get_registered_services(self):
        """Test getting list of registered services"""
        # Initially only container itself is registered
        initial_services = self.container.get_registered_services()
        assert DependencyInjectionContainer in initial_services
        
        # Register test service
        self.container.register_singleton(TestService)
        
        # Should include test service
        services = self.container.get_registered_services()
        assert TestService in services
        assert len(services) == len(initial_services) + 1
    
    def test_get_service_info(self):
        """Test getting service information"""
        # Register service
        self.container.register_singleton(TestService)
        
        # Get service info
        info = self.container.get_service_info(TestService)
        
        assert info is not None
        assert info['service_type'] == 'TestService'
        assert info['lifetime'] == 'singleton'
        assert not info['has_factory']
        assert not info['has_instance']
    
    def test_method_chaining(self):
        """Test method chaining for fluent API"""
        # Should be able to chain registration methods
        result = (self.container
                 .register_singleton(TestService)
                 .register_transient(TestServiceWithDependency))
        
        # Should return the container for chaining
        assert result is self.container
        
        # Both services should be registered
        assert self.container.is_registered(TestService)
        assert self.container.is_registered(TestServiceWithDependency)

class TestGlobalContainer(BaseTestCase):
    """Test cases for global container functions"""
    
    def test_get_container_singleton(self):
        """Test that get_container returns singleton"""
        container1 = get_container()
        container2 = get_container()
        
        assert container1 is container2
    
    def test_configure_services(self):
        """Test service configuration"""
        container = DependencyInjectionContainer()
        
        # Should not raise exception even if services don't exist
        configure_services(container)
        
        # Container should have some services registered
        services = container.get_registered_services()
        assert len(services) > 0  # At least the container itself

class TestServiceLifetimeScenarios(BaseTestCase):
    """Test various service lifetime scenarios"""
    
    def setup_method(self):
        super().setup_method()
        self.container = DependencyInjectionContainer()
    
    def test_mixed_lifetimes(self):
        """Test services with different lifetimes"""
        class SingletonService:
            def __init__(self):
                self.id = id(self)
        
        class TransientService:
            def __init__(self, singleton: SingletonService):
                self.singleton = singleton
                self.id = id(self)
        
        # Register with different lifetimes
        self.container.register_singleton(SingletonService)
        self.container.register_transient(TransientService)
        
        # Resolve multiple times
        transient1 = self.container.resolve(TransientService)
        transient2 = self.container.resolve(TransientService)
        
        # Transient services should be different
        assert transient1.id != transient2.id
        
        # But they should share the same singleton dependency
        assert transient1.singleton is transient2.singleton
    
    def test_factory_with_dependencies(self):
        """Test factory that creates service with dependencies"""
        self.container.register_singleton(TestService)
        
        def create_service_with_dependency():
            dependency = self.container.resolve(TestService)
            return TestServiceWithDependency(dependency)
        
        self.container.register_factory(
            TestServiceWithDependency, 
            create_service_with_dependency,
            ServiceLifetime.TRANSIENT
        )
        
        # Resolve service created by factory
        instance = self.container.resolve(TestServiceWithDependency)
        
        assert isinstance(instance, TestServiceWithDependency)
        assert isinstance(instance.dependency, TestService)

if __name__ == "__main__":
    pytest.main([__file__])
